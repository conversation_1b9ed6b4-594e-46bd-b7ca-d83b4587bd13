#!/usr/bin/env python3
"""
颜色兼容性测试程序
用于验证MaixPy颜色定义是否正确
"""

# 模拟MaixPy环境进行测试
try:
    from maix import image, display, app, time
    MAIX_AVAILABLE = True
    print("MaixPy环境可用")
except ImportError:
    MAIX_AVAILABLE = False
    print("MaixPy环境不可用，使用模拟模式")

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255)

def test_colors():
    """测试颜色定义"""
    colors = {
        'WHITE': WHITE,
        'BLACK': BLACK,
        'RED': RED,
        'GREEN': GREEN,
        'BLUE': BLUE,
        'YELLOW': Y<PERSON>LOW,
        'CYAN': <PERSON>YAN,
        'MAGENTA': MAGENTA
    }
    
    print("颜色定义测试:")
    for name, color in colors.items():
        print(f"{name}: {color}")
    
    if MAIX_AVAILABLE:
        print("\n创建测试图像...")
        try:
            img = image.Image(320, 240, image.Format.FMT_RGB888)
            
            # 测试绘制函数
            img.draw_string(10, 10, "颜色测试", WHITE)
            img.draw_line(10, 30, 100, 30, RED, 2)
            img.draw_rect(10, 40, 50, 30, GREEN, 2)
            img.draw_circle(50, 100, 20, BLUE, 2)
            
            print("✅ 所有绘图函数测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 绘图测试失败: {e}")
            return False
    else:
        print("✅ 颜色定义格式正确")
        return True

if __name__ == "__main__":
    print("=" * 40)
    print("MaixPy 颜色兼容性测试")
    print("=" * 40)
    
    success = test_colors()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ 测试通过！颜色定义兼容")
    else:
        print("❌ 测试失败！需要修复")
    print("=" * 40)
