# 二维云台画图系统 - 图像大小控制指南

## 🎯 **控制图像大小的四种方法**

---

## 📐 **方法1: 调整绘图区域范围（全局缩放）**

### **原理说明**
通过修改 `DRAW_AREA_X` 和 `DRAW_AREA_Y` 参数来控制舵机的活动范围，从而改变所有图形的整体大小。

### **当前设置**
```python
DRAW_AREA_X = (30, 150)  # X轴角度范围: 120°
DRAW_AREA_Y = (30, 150)  # Y轴角度范围: 120°
```

### **大小调整示例**

#### **🔍 缩小图像 (精细绘图)**
```python
# 缩小到60°范围 - 图像变小，精度提高
DRAW_AREA_X = (60, 120)  # 60°范围
DRAW_AREA_Y = (60, 120)  # 60°范围

# 效果: 所有图形缩小50%，但绘制更精细
```

#### **🔍 放大图像 (大幅绘图)**
```python
# 扩大到140°范围 - 图像变大，覆盖更大区域
DRAW_AREA_X = (20, 160)  # 140°范围
DRAW_AREA_Y = (20, 160)  # 140°范围

# 效果: 所有图形放大17%，绘图区域更大
```

#### **🔍 极小图像 (高精度)**
```python
# 超小范围 - 适合精密绘图
DRAW_AREA_X = (80, 100)  # 20°范围
DRAW_AREA_Y = (80, 100)  # 20°范围

# 效果: 图形缩小到原来的17%，极高精度
```

### **大小对比表**

| 角度范围 | 相对大小 | 适用场景 | 精度等级 |
|----------|----------|----------|----------|
| 20° | 17% | 精密绘图、细节工作 | ⭐⭐⭐⭐⭐ |
| 60° | 50% | 小型图案、测试 | ⭐⭐⭐⭐ |
| 120° | 100% | 标准绘图（当前） | ⭐⭐⭐ |
| 140° | 117% | 大型图案 | ⭐⭐ |
| 160° | 133% | 最大绘图区域 | ⭐ |

---

## 🎨 **方法2: 修改图形参数（单独缩放）**

### **原理说明**
直接修改每个图形的尺寸参数，实现单独的大小控制。

### **当前图形参数**
```python
paths = [
    line_path((80, 80), (240, 160), 30),    # 直线: 起点到终点
    circle_path(160, 120, 50),              # 圆形: 半径50
    rect_path(100, 80, 120, 80),            # 矩形: 120x80
    triangle_path(160, 120, 80),            # 三角形: 边长80
    star_path(160, 120, 60)                 # 五角星: 外径60
]
```

### **大小调整示例**

#### **🔍 缩小所有图形**
```python
paths = [
    line_path((120, 100), (200, 140), 30),  # 直线缩短
    circle_path(160, 120, 25),              # 圆形半径减半
    rect_path(130, 100, 60, 40),            # 矩形缩小50%
    triangle_path(160, 120, 40),            # 三角形缩小50%
    star_path(160, 120, 30)                 # 五角星缩小50%
]
```

#### **🔍 放大所有图形**
```python
paths = [
    line_path((40, 40), (280, 200), 30),    # 直线延长
    circle_path(160, 120, 80),              # 圆形半径增大
    rect_path(80, 60, 160, 120),            # 矩形放大33%
    triangle_path(160, 120, 120),           # 三角形放大50%
    star_path(160, 120, 90)                 # 五角星放大50%
]
```

#### **🔍 个别图形调整**
```python
# 只放大圆形，其他保持不变
paths = [
    line_path((80, 80), (240, 160), 30),
    circle_path(160, 120, 100),             # 只有圆形变大
    rect_path(100, 80, 120, 80),
    triangle_path(160, 120, 80),
    star_path(160, 120, 60)
]
```

---

## ⚙️ **方法3: 动态缩放系数（程序控制）**

### **实现思路**
添加一个全局缩放系数，程序运行时动态调整图形大小。

### **代码实现**
```python
# 添加缩放系数
SCALE_FACTOR = 1.0  # 1.0=原始大小, 0.5=缩小50%, 2.0=放大100%

def scaled_circle_path(cx, cy, r, res=32):
    """带缩放的圆形路径"""
    scaled_r = r * SCALE_FACTOR
    return circle_path(cx, cy, scaled_r, res)

def scaled_rect_path(x, y, w, h):
    """带缩放的矩形路径"""
    scaled_w = w * SCALE_FACTOR
    scaled_h = h * SCALE_FACTOR
    # 保持中心位置不变
    center_x, center_y = x + w/2, y + h/2
    new_x = center_x - scaled_w/2
    new_y = center_y - scaled_h/2
    return rect_path(new_x, new_y, scaled_w, scaled_h)
```

### **使用示例**
```python
# 缩小到70%
SCALE_FACTOR = 0.7

# 放大到150%
SCALE_FACTOR = 1.5

# 微调到90%
SCALE_FACTOR = 0.9
```

---

## 🎛️ **方法4: 用户交互控制（高级功能）**

### **实现思路**
通过按键或触摸屏实现实时的大小调整。

### **代码框架**
```python
class DrawingEngine:
    def __init__(self):
        self.gimbal = Gimbal()
        self.shapes = ["直线", "圆形", "矩形", "三角形", "五角星"]
        self.size_scale = 1.0  # 当前缩放比例
        
    def adjust_size(self, delta):
        """调整图形大小"""
        self.size_scale += delta
        self.size_scale = max(0.2, min(3.0, self.size_scale))  # 限制范围
        print(f"图形大小调整为: {self.size_scale*100:.0f}%")
        
    def get_scaled_paths(self, shape_index):
        """获取缩放后的路径"""
        base_paths = [
            line_path((80, 80), (240, 160), 30),
            circle_path(160, 120, 50),
            rect_path(100, 80, 120, 80),
            triangle_path(160, 120, 80),
            star_path(160, 120, 60)
        ]
        
        # 应用缩放（这里需要实现具体的缩放逻辑）
        return self.apply_scale(base_paths[shape_index])
```

---

## 📊 **实际应用建议**

### **🎯 根据应用场景选择方法**

#### **精密绘图场景**
```python
# 方法1: 缩小绘图区域
DRAW_AREA_X = (70, 110)  # 40°范围
DRAW_AREA_Y = (70, 110)  # 40°范围

# 方法2: 缩小图形参数
circle_path(160, 120, 20)  # 小圆形
```

#### **展示演示场景**
```python
# 方法1: 扩大绘图区域
DRAW_AREA_X = (15, 165)  # 150°范围
DRAW_AREA_Y = (15, 165)  # 150°范围

# 方法2: 放大图形参数
circle_path(160, 120, 90)  # 大圆形
```

#### **测试调试场景**
```python
# 方法3: 使用动态缩放
SCALE_FACTOR = 0.3  # 缩小到30%，便于快速测试
```

### **🔧 参数调整技巧**

1. **保持比例**: 调整时保持X和Y轴的比例一致
2. **渐进调整**: 每次调整10-20%，避免过大变化
3. **安全边界**: 确保角度不超出舵机物理限制
4. **测试验证**: 每次调整后测试所有图形

### **⚠️ 注意事项**

1. **机械限制**: 角度范围不能超出舵机物理限制
2. **精度平衡**: 过小的范围可能导致精度损失
3. **中心对齐**: 调整时保持图形中心位置
4. **边界检查**: 确保图形不会超出绘图区域

---

## 🎨 **快速调整模板**

### **小图精密模式**
```python
DRAW_AREA_X = (75, 105)  # 30°范围
DRAW_AREA_Y = (75, 105)  # 30°范围
# 图形缩小到25%，精度提高4倍
```

### **标准绘图模式**
```python
DRAW_AREA_X = (30, 150)  # 120°范围（当前）
DRAW_AREA_Y = (30, 150)  # 120°范围（当前）
# 平衡大小和精度
```

### **大图展示模式**
```python
DRAW_AREA_X = (10, 170)  # 160°范围
DRAW_AREA_Y = (10, 170)  # 160°范围
# 图形放大33%，覆盖最大区域
```

选择最适合您需求的方法，就能完美控制绘制图像的大小！
