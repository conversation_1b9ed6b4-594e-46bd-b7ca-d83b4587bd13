# 二维云台画图系统 - 角度换算思路详解

## 🎯 **核心思路概述**

我们的角度换算采用**三层坐标系转换**的思路：
```
像素坐标 → 画布坐标 → 舵机角度 → PWM占空比
```

---

## 📐 **第一层：像素坐标系统**

### **坐标系定义**
```python
CANVAS_W, CANVAS_H = 320, 240  # 画布尺寸
```

- **原点位置**: 左上角 (0, 0)
- **X轴方向**: 向右为正
- **Y轴方向**: 向下为正
- **坐标范围**: X: 0~320, Y: 0~240

### **实际应用**
```python
# 例如绘制一个圆形
center_x, center_y = 160, 120  # 画布中心
radius = 50
# 生成的像素坐标点在 (110,70) 到 (210,170) 范围内
```

---

## 🔄 **第二层：坐标映射算法**

### **核心算法**
```python
def pixel_to_angle(x, y):
    # 计算缩放因子
    scale_x = (DRAW_AREA_X[1] - DRAW_AREA_X[0]) / CANVAS_W
    scale_y = (DRAW_AREA_Y[1] - DRAW_AREA_Y[0]) / CANVAS_H
    
    # 线性映射
    servo_x = x * scale_x + DRAW_AREA_X[0]
    servo_y = y * scale_y + DRAW_AREA_Y[0]
    
    # 安全限制
    servo_x = max(0, min(180, servo_x))
    servo_y = max(0, min(180, servo_y))
    
    return servo_x, servo_y
```

### **数学原理**

#### **1. 缩放因子计算**
```
scale_x = (150 - 30) / 320 = 120 / 320 = 0.375
scale_y = (150 - 30) / 240 = 120 / 240 = 0.5
```

#### **2. 线性映射公式**
```
servo_angle = pixel_coord * scale_factor + offset
```

**具体展开**：
- `servo_x = x * 0.375 + 30`
- `servo_y = y * 0.5 + 30`

#### **3. 映射范围对照表**

| 像素坐标 | 舵机角度 | 说明 |
|----------|----------|------|
| X: 0 → 320 | X: 30° → 150° | 水平方向映射 |
| Y: 0 → 240 | Y: 30° → 150° | 垂直方向映射 |

### **设计思路解析**

#### **为什么选择 30°~150° 范围？**
1. **安全考虑**: 避免舵机机械限位
2. **绘图精度**: 120°范围提供足够的绘图空间
3. **中心对称**: 90°为中心，左右各60°

#### **为什么使用线性映射？**
1. **简单高效**: 计算量小，实时性好
2. **精度均匀**: 整个绘图区域精度一致
3. **易于调试**: 映射关系直观明了

---

## ⚙️ **第三层：PWM控制转换**

### **角度到PWM占空比**
```python
def set_angle(self, pan_angle, tilt_angle):
    # 角度限制 (0°~180°)
    pan_angle = max(0, min(180, pan_angle))
    tilt_angle = max(0, min(180, tilt_angle))
    
    # PWM占空比计算
    pan_duty = 2.5 + 10 * pan_angle / 180
    tilt_duty = 2.5 + 10 * tilt_angle / 180
```

### **PWM公式推导**

#### **舵机PWM标准**
- **频率**: 50Hz (20ms周期)
- **脉宽范围**: 0.5ms~2.5ms
- **角度范围**: 0°~180°

#### **占空比计算**
```
占空比 = 脉宽 / 周期 * 100%

0°:   0.5ms / 20ms = 2.5%
90°:  1.5ms / 20ms = 7.5%  
180°: 2.5ms / 20ms = 12.5%
```

#### **线性公式**
```
duty = 2.5 + (12.5 - 2.5) * angle / 180
duty = 2.5 + 10 * angle / 180
```

---

## 📊 **完整换算示例**

### **示例1: 画布中心点**
```python
# 输入: 像素坐标 (160, 120) - 画布中心
x, y = 160, 120

# 第一步: 计算缩放因子
scale_x = 120 / 320 = 0.375
scale_y = 120 / 240 = 0.5

# 第二步: 坐标映射
servo_x = 160 * 0.375 + 30 = 60 + 30 = 90°
servo_y = 120 * 0.5 + 30 = 60 + 30 = 90°

# 第三步: PWM转换
pan_duty = 2.5 + 10 * 90 / 180 = 2.5 + 5 = 7.5%
tilt_duty = 2.5 + 10 * 90 / 180 = 2.5 + 5 = 7.5%

# 结果: 舵机回到中心位置 (90°, 90°)
```

### **示例2: 画布左上角**
```python
# 输入: 像素坐标 (0, 0) - 画布左上角
x, y = 0, 0

# 坐标映射
servo_x = 0 * 0.375 + 30 = 30°
servo_y = 0 * 0.5 + 30 = 30°

# PWM转换
pan_duty = 2.5 + 10 * 30 / 180 = 2.5 + 1.67 = 4.17%
tilt_duty = 2.5 + 10 * 30 / 180 = 2.5 + 1.67 = 4.17%

# 结果: 舵机移动到绘图区域左上角
```

### **示例3: 画布右下角**
```python
# 输入: 像素坐标 (320, 240) - 画布右下角
x, y = 320, 240

# 坐标映射
servo_x = 320 * 0.375 + 30 = 120 + 30 = 150°
servo_y = 240 * 0.5 + 30 = 120 + 30 = 150°

# PWM转换
pan_duty = 2.5 + 10 * 150 / 180 = 2.5 + 8.33 = 10.83%
tilt_duty = 2.5 + 10 * 150 / 180 = 2.5 + 8.33 = 10.83%

# 结果: 舵机移动到绘图区域右下角
```

---

## 🎛️ **参数调优策略**

### **绘图区域调整**
```python
# 当前设置
DRAW_AREA_X = (30, 150)  # X轴角度范围
DRAW_AREA_Y = (30, 150)  # Y轴角度范围

# 扩大绘图区域
DRAW_AREA_X = (20, 160)  # 增加20°范围
DRAW_AREA_Y = (20, 160)

# 缩小绘图区域（提高精度）
DRAW_AREA_X = (45, 135)  # 减少30°范围
DRAW_AREA_Y = (45, 135)
```

### **精度与范围的权衡**
- **大范围**: 绘图区域大，但精度相对较低
- **小范围**: 绘图精度高，但可绘制区域小
- **当前选择**: 平衡精度和范围，120°提供良好的绘图体验

---

## 🔧 **算法优势**

1. **计算高效**: 只需简单的乘法和加法
2. **精度稳定**: 线性映射保证精度均匀分布
3. **易于调试**: 映射关系直观，便于参数调整
4. **安全可靠**: 多层限制确保舵机安全运行
5. **扩展性好**: 可轻松调整绘图区域和精度

---

## 📐 **核心设计哲学**

**"简单而精确"** - 用最简单的数学模型实现最精确的控制效果。

这种设计思路确保了系统的**稳定性**、**可维护性**和**高性能**。
