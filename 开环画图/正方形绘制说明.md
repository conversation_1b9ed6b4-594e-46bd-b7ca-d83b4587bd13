# 正方形连续绘制系统

## 🎯 **功能说明**

系统已修改为专门绘制正方形，提供两个版本：

### **版本1: 简单正方形绘制** (`maixcam_drawing.py`)
- **功能**: 连续绘制不同大小的正方形
- **特点**: 简单直接，5种大小循环绘制
- **间隔**: 每5秒绘制一个正方形

### **版本2: 高级正方形绘制** (`square_drawing.py`)
- **功能**: 多种绘制模式，大小和位置都会变化
- **特点**: 3种模式自动切换，更丰富的变化
- **间隔**: 每4秒绘制一个正方形

---

## 🔧 **版本1: 简单正方形绘制**

### **运行方式**
```bash
python maixcam_drawing.py
```

### **绘制规律**
- **大小序列**: 40×40 → 60×60 → 80×80 → 100×100 → 120×120 → 循环
- **位置**: 始终居中绘制
- **间隔**: 每5秒绘制一个
- **显示**: 实时预览下一个正方形大小

### **界面信息**
```
MaixCAM 正方形绘制
下个正方形: 80x80
角度: (90.0, 90.0)
下次绘制: 3秒
连续绘制正方形
```

---

## 🎨 **版本2: 高级正方形绘制**

### **运行方式**
```bash
python square_drawing.py
```

### **三种绘制模式**

#### **模式1: 大小变化**
- **特点**: 固定中心位置，变化大小
- **大小**: 30×30 → 50×50 → 70×70 → 90×90 → 110×110
- **位置**: 始终居中

#### **模式2: 位置变化**
- **特点**: 固定大小60×60，变化位置
- **位置**: 左上 → 右上 → 右下 → 左下 → 中心
- **大小**: 固定60×60

#### **模式3: 组合模式**
- **特点**: 大小和位置都变化
- **变化**: 随机组合不同大小和位置
- **效果**: 最丰富的视觉效果

### **自动切换**
- **绘制间隔**: 每4秒一个正方形
- **模式切换**: 每20秒自动切换模式
- **循环**: 3种模式无限循环

### **界面信息**
```
正方形绘制系统
模式: 大小变化
角度: (90.0, 90.0)
下次绘制: 2秒
模式切换: 15秒
连续绘制正方形
```

---

## ⚙️ **参数调整**

### **修改绘制间隔**
```python
# 在主程序中修改
if current_time - last_draw_time > 5000:  # 5秒间隔
    # 改为3秒间隔
if current_time - last_draw_time > 3000:  # 3秒间隔
```

### **修改正方形大小**
```python
# 版本1: 修改大小数组
self.square_sizes = [40, 60, 80, 100, 120]
# 改为更小的正方形
self.square_sizes = [20, 30, 40, 50, 60]
# 改为更大的正方形  
self.square_sizes = [60, 80, 100, 120, 140]
```

### **修改绘制速度**
```python
# 修改步进延迟
STEP_DELAY = 60  # 毫秒，越小越快
# 更快绘制
STEP_DELAY = 40
# 更慢绘制
STEP_DELAY = 100
```

### **修改绘图区域大小**
```python
# 当前设置
DRAW_AREA_X, DRAW_AREA_Y = (30, 150), (30, 150)  # 120°范围

# 缩小绘图区域（正方形变小）
DRAW_AREA_X, DRAW_AREA_Y = (60, 120), (60, 120)  # 60°范围

# 扩大绘图区域（正方形变大）
DRAW_AREA_X, DRAW_AREA_Y = (15, 165), (15, 165)  # 150°范围
```

---

## 🎛️ **自定义正方形序列**

### **创建自定义大小序列**
```python
# 递增序列
self.square_sizes = [20, 40, 60, 80, 100, 120, 140]

# 递减序列
self.square_sizes = [120, 100, 80, 60, 40, 20]

# 波动序列
self.square_sizes = [50, 100, 30, 90, 40, 110, 60]

# 只画小正方形
self.square_sizes = [20, 25, 30, 35, 40]

# 只画大正方形
self.square_sizes = [100, 110, 120, 130, 140]
```

### **创建自定义位置序列**
```python
# 圆形排列
self.positions = [
    (160, 80),   # 上
    (200, 120),  # 右
    (160, 160),  # 下
    (120, 120),  # 左
    (160, 120)   # 中心
]

# 对角线排列
self.positions = [
    (100, 80),   # 左上
    (140, 100),  # 
    (180, 120),  # 
    (220, 140),  # 右下
    (160, 120)   # 中心
]
```

---

## 📊 **效果对比**

| 特性 | 版本1 (简单) | 版本2 (高级) |
|------|-------------|-------------|
| **文件大小** | 194行 | 250行 |
| **绘制模式** | 1种 | 3种 |
| **变化类型** | 仅大小 | 大小+位置 |
| **切换方式** | 固定循环 | 自动切换 |
| **适用场景** | 简单演示 | 丰富展示 |
| **资源占用** | 更低 | 稍高 |

---

## 🚀 **快速开始**

### **推荐使用**
- **简单需求**: 使用 `maixcam_drawing.py`
- **丰富效果**: 使用 `square_drawing.py`

### **一键运行**
```bash
# 简单版本
python maixcam_drawing.py

# 高级版本  
python square_drawing.py
```

### **停止绘制**
- 按设备上的功能键
- 或在MaixVision中点击停止按钮

---

**现在系统将连续不断地绘制正方形，每个正方形的大小都会变化，创造出丰富的视觉效果！**
