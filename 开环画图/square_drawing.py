#!/usr/bin/env python3
"""
MaixCAM 专用正方形绘制系统
连续绘制不同大小和位置的正方形
"""
from maix import camera, display, app, time, pwm, pinmap, image
import math

# 颜色定义
WHITE = image.Color.from_rgb(255, 255, 255)
RED = image.Color.from_rgb(255, 0, 0)
GREEN = image.Color.from_rgb(0, 255, 0)
YELLOW = image.Color.from_rgb(255, 255, 0)
CYAN = image.Color.from_rgb(0, 255, 255)

# 配置参数
CANVAS_W, CANVAS_H = 320, 240
DRAW_AREA_X, DRAW_AREA_Y = (30, 150), (30, 150)
STEP_DELAY = 30  # 加快绘制速度

# 舵机控制
class Gimbal:
    def __init__(self):
        pinmap.set_pin_function("A19", "PWM7")
        pinmap.set_pin_function("A18", "PWM6")
        self.pan = pwm.PWM(7, freq=50, duty=7.5, enable=True)
        self.tilt = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        self.pan_angle = self.tilt_angle = 90

    def set_angle(self, pan_angle, tilt_angle):
        pan_angle = max(0, min(180, pan_angle))
        tilt_angle = max(0, min(180, tilt_angle))
        self.pan.duty(2.5 + 10 * pan_angle / 180)
        self.tilt.duty(2.5 + 10 * tilt_angle / 180)
        self.pan_angle, self.tilt_angle = pan_angle, tilt_angle

    def center(self):
        self.set_angle(90, 90)

# 坐标转换
def pixel_to_angle(x, y):
    scale_x = (DRAW_AREA_X[1] - DRAW_AREA_X[0]) / CANVAS_W
    scale_y = (DRAW_AREA_Y[1] - DRAW_AREA_Y[0]) / CANVAS_H
    servo_x = max(0, min(180, x * scale_x + DRAW_AREA_X[0]))
    servo_y = max(0, min(180, y * scale_y + DRAW_AREA_Y[0]))
    return servo_x, servo_y

# 路径生成
def line_path(p1, p2, steps=15):
    return [(int(p1[0] + i*(p2[0]-p1[0])/steps), int(p1[1] + i*(p2[1]-p1[1])/steps)) for i in range(steps+1)]

def square_path(x, y, size):
    """生成正方形路径"""
    p1 = (x, y)                    # 左上角
    p2 = (x + size, y)             # 右上角
    p3 = (x + size, y + size)      # 右下角
    p4 = (x, y + size)             # 左下角
    
    path = []
    path.extend(line_path(p1, p2, 12))  # 上边
    path.extend(line_path(p2, p3, 12))  # 右边
    path.extend(line_path(p3, p4, 12))  # 下边
    path.extend(line_path(p4, p1, 12))  # 左边
    return path

# 正方形绘制引擎
class SquareDrawingEngine:
    def __init__(self):
        self.gimbal = Gimbal()
        
        # 正方形变化模式
        self.modes = [
            {"name": "大小变化", "type": "size"},
            {"name": "位置变化", "type": "position"}, 
            {"name": "组合模式", "type": "combo"}
        ]
        self.current_mode = 0
        
        # 大小变化参数
        self.sizes = [30, 50, 70, 90, 110]
        self.size_index = 0
        
        # 位置变化参数
        self.positions = [
            (120, 80),   # 左上
            (200, 80),   # 右上
            (200, 160),  # 右下
            (120, 160),  # 左下
            (160, 120)   # 中心
        ]
        self.pos_index = 0
        
        # 组合模式参数
        self.combo_patterns = [
            {"size": 40, "pos": (140, 100)},
            {"size": 60, "pos": (160, 120)},
            {"size": 80, "pos": (140, 100)},
            {"size": 100, "pos": (110, 90)},
            {"size": 50, "pos": (180, 140)}
        ]
        self.combo_index = 0
        
    def get_next_square(self):
        """获取下一个要绘制的正方形参数"""
        mode = self.modes[self.current_mode]
        
        if mode["type"] == "size":
            # 大小变化模式：固定位置，变化大小
            size = self.sizes[self.size_index]
            pos = (160 - size//2, 120 - size//2)  # 居中
            self.size_index = (self.size_index + 1) % len(self.sizes)
            return pos[0], pos[1], size, f"大小: {size}x{size}"
            
        elif mode["type"] == "position":
            # 位置变化模式：固定大小，变化位置
            size = 60
            pos = self.positions[self.pos_index]
            self.pos_index = (self.pos_index + 1) % len(self.positions)
            return pos[0] - size//2, pos[1] - size//2, size, f"位置: ({pos[0]}, {pos[1]})"
            
        elif mode["type"] == "combo":
            # 组合模式：大小和位置都变化
            pattern = self.combo_patterns[self.combo_index]
            size = pattern["size"]
            pos = pattern["pos"]
            self.combo_index = (self.combo_index + 1) % len(self.combo_patterns)
            return pos[0] - size//2, pos[1] - size//2, size, f"组合: {size}x{size} @ ({pos[0]}, {pos[1]})"
    
    def draw_square(self):
        """绘制一个正方形"""
        x, y, size, description = self.get_next_square()
        
        # 生成正方形路径
        path = square_path(x, y, size)
        
        print(f"绘制正方形: {description}")
        self.execute_path(path)
        
    def switch_mode(self):
        """切换绘制模式"""
        self.current_mode = (self.current_mode + 1) % len(self.modes)
        print(f"切换到模式: {self.modes[self.current_mode]['name']}")
        
    def execute_path(self, path):
        """执行绘制路径"""
        if not path: return
        print(f"执行路径: {len(path)}点")
        
        # 移动到起始位置
        start_x, start_y = pixel_to_angle(path[0][0], path[0][1])
        self.gimbal.set_angle(start_x, start_y)
        time.sleep_ms(300)
        
        # 执行绘制
        for i, (x, y) in enumerate(path):
            if app.need_exit(): break
            servo_x, servo_y = pixel_to_angle(x, y)
            self.gimbal.set_angle(servo_x, servo_y)
            time.sleep_ms(STEP_DELAY)
            
        print("正方形绘制完成")
        time.sleep_ms(500)

# 主程序
def main():
    print("=== MaixCAM 正方形绘制系统 ===")
    try:
        cam = camera.Camera(320, 240, image.Format.FMT_RGB888)
        disp = display.Display()
        engine = SquareDrawingEngine()
        engine.gimbal.center()
        time.sleep_ms(2000)
        
        last_draw_time = 0
        last_mode_switch = 0
        
        while not app.need_exit():
            img = cam.read()
            current_time = time.time_ms()
            
            # 界面显示
            img.draw_string(10, 10, "正方形绘制系统", WHITE, scale=1.5)
            mode_name = engine.modes[engine.current_mode]["name"]
            img.draw_string(10, 35, f"模式: {mode_name}", GREEN)
            img.draw_string(10, 55, f"角度: ({engine.gimbal.pan_angle:.1f}, {engine.gimbal.tilt_angle:.1f})", CYAN)
            
            # 预览下一个正方形
            next_x, next_y, next_size, next_desc = engine.get_next_square()
            # 恢复索引（因为get_next_square会改变索引）
            if engine.modes[engine.current_mode]["type"] == "size":
                engine.size_index = (engine.size_index - 1) % len(engine.sizes)
            elif engine.modes[engine.current_mode]["type"] == "position":
                engine.pos_index = (engine.pos_index - 1) % len(engine.positions)
            elif engine.modes[engine.current_mode]["type"] == "combo":
                engine.combo_index = (engine.combo_index - 1) % len(engine.combo_patterns)
            
            # 绘制预览（缩小显示）
            preview_size = min(40, next_size // 2)
            preview_x = 160 - preview_size // 2
            preview_y = 120 - preview_size // 2
            img.draw_rect(preview_x, preview_y, preview_size, preview_size, RED, thickness=2)
            
            # 状态信息
            next_draw = 4000 - (current_time % 4000)
            img.draw_string(10, 180, f"下次绘制: {next_draw//1000}秒", YELLOW)
            
            next_mode_switch = 20000 - (current_time % 20000)
            img.draw_string(10, 200, f"模式切换: {next_mode_switch//1000}秒", YELLOW)
            
            img.draw_string(10, 220, "连续绘制正方形", WHITE)
            
            # 自动绘制正方形（每4秒）
            if current_time - last_draw_time > 4000:
                img.draw_string(10, 100, "正在绘制正方形...", RED, scale=1.5)
                disp.show(img)
                engine.draw_square()
                last_draw_time = current_time
            
            # 自动切换模式（每20秒）
            if current_time - last_mode_switch > 20000:
                engine.switch_mode()
                last_mode_switch = current_time
            
            disp.show(img)
            time.sleep_ms(50)
            
    except Exception as e:
        print(f"错误: {e}")
    finally:
        print("系统退出")

if __name__ == "__main__":
    main()
