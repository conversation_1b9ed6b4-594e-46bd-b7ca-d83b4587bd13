#!/usr/bin/env python3
"""
MaixCAM 二维云台画图系统 - 精简版
"""
from maix import camera, display, app, time, pwm, pinmap, image
import math

# 颜色定义 - 使用官方API
WHITE = image.Color.from_rgb(255, 255, 255)
RED = image.Color.from_rgb(255, 0, 0)
GREEN = image.Color.from_rgb(0, 255, 0)
YELLOW = image.Color.from_rgb(255, 255, 0)
CYAN = image.Color.from_rgb(0, 255, 255)

# 配置参数
CANVAS_W, CANVAS_H = 320, 240

# 图像大小控制 - 修改这里调整图像大小
# 标准模式: (30, 150) = 120°范围 = 100%大小
# 小图模式: (60, 120) = 60°范围 = 50%大小
# 大图模式: (15, 165) = 150°范围 = 125%大小
# 精密模式: (80, 100) = 20°范围 = 17%大小
DRAW_AREA_X, DRAW_AREA_Y = (60, 120),(60, 120)  # 当前: 标准模式

STEP_DELAY = 80


# 舵机控制
class Gimbal:
    def __init__(self):
        pinmap.set_pin_function("A19", "PWM7")
        pinmap.set_pin_function("A18", "PWM6")
        self.pan = pwm.PWM(7, freq=50, duty=7.5, enable=True)
        self.tilt = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        self.pan_angle = self.tilt_angle = 90

    def set_angle(self, pan_angle, tilt_angle):
        pan_angle = max(0, min(180, pan_angle))
        tilt_angle = max(0, min(180, tilt_angle))
        self.pan.duty(2.5 + 10 * pan_angle / 180)
        self.tilt.duty(2.5 + 10 * tilt_angle / 180)
        self.pan_angle, self.tilt_angle = pan_angle, tilt_angle

    def center(self):
        self.set_angle(90, 90)


# 坐标转换
def pixel_to_angle(x, y):
    scale_x = (DRAW_AREA_X[1] - DRAW_AREA_X[0]) / CANVAS_W
    scale_y = (DRAW_AREA_Y[1] - DRAW_AREA_Y[0]) / CANVAS_H
    servo_x = max(0, min(180, x * scale_x + DRAW_AREA_X[0]))
    servo_y = max(0, min(180, y * scale_y + DRAW_AREA_Y[0]))
    return servo_x, servo_y


# 路径生成
def line_path(p1, p2, steps=20):
    return [(int(p1[0] + i*(p2[0]-p1[0])/steps), int(p1[1] + i*(p2[1]-p1[1])/steps)) for i in range(steps+1)]

def circle_path(cx, cy, r, res=32):
    return [(int(cx + r*math.cos(2*math.pi*i/res)), int(cy + r*math.sin(2*math.pi*i/res))) for i in range(res+1)]

def rect_path(x, y, w, h):
    path = line_path((x,y), (x+w,y), 15)
    path.extend(line_path((x+w,y), (x+w,y+h), 15))
    path.extend(line_path((x+w,y+h), (x,y+h), 15))
    path.extend(line_path((x,y+h), (x,y), 15))
    return path

def triangle_path(cx, cy, size):
    h = size * 0.866
    p1, p2, p3 = (cx, cy-h*0.67), (cx-size/2, cy+h*0.33), (cx+size/2, cy+h*0.33)
    path = line_path(p1, p2, 10)
    path.extend(line_path(p2, p3, 10))
    path.extend(line_path(p3, p1, 10))
    return path

def star_path(cx, cy, r):
    points = []
    for i in range(10):
        a = math.pi * i / 5 - math.pi / 2
        radius = r if i % 2 == 0 else r * 0.4
        points.append((int(cx + radius*math.cos(a)), int(cy + radius*math.sin(a))))
    path = []
    for i in range(10):
        path.extend(line_path(points[i], points[(i+1)%10], 8))
    return path


# 绘图引擎
class DrawingEngine:
    def __init__(self):
        self.gimbal = Gimbal()
        self.shapes = ["正方形"]  # 只保留正方形
        self.square_sizes = [40, 60, 80, 100, 120]  # 不同大小的正方形
        self.current_size_index = 0

    def draw_shape(self, shape_index=0):
        # 获取当前正方形大小
        size = self.square_sizes[self.current_size_index]

        # 计算正方形位置（居中）
        center_x, center_y = 160, 120
        x = center_x - size // 2
        y = center_y - size // 2

        # 生成正方形路径（矩形的长宽相等就是正方形）
        square_path = rect_path(x, y, size, size)

        print(f"绘制: 正方形 (大小: {size}x{size})")
        self.execute_path(square_path)

        # 切换到下一个大小
        self.current_size_index = (self.current_size_index + 1) % len(self.square_sizes)

    def execute_path(self, path):
        if not path: return
        print(f"执行路径: {len(path)}点")

        # 移动到起始位置
        start_x, start_y = pixel_to_angle(path[0][0], path[0][1])
        self.gimbal.set_angle(start_x, start_y)
        time.sleep_ms(500)

        # 执行绘制
        for i, (x, y) in enumerate(path):
            if app.need_exit(): break
            servo_x, servo_y = pixel_to_angle(x, y)
            self.gimbal.set_angle(servo_x, servo_y)
            time.sleep_ms(STEP_DELAY)
            if i % 20 == 0:
                print(f"进度: {(i+1)/len(path)*100:.1f}%")

        print("绘制完成")
        time.sleep_ms(1000)
        self.gimbal.center()


# 主程序
def main():
    print("=== MaixCAM 二维云台画图系统 ===")
    try:
        # 使用官方推荐的初始化方式
        cam = camera.Camera(320, 240, image.Format.FMT_RGB888)
        disp = display.Display()
        engine = DrawingEngine()
        engine.gimbal.center()
        time.sleep_ms(2000)

        last_draw_time = 0  # 不需要shape_index了

        while not app.need_exit():
            img = cam.read()

            # 界面显示
            img.draw_string(10, 10, "MaixCAM 正方形绘制", WHITE, scale=1.5)
            current_size = engine.square_sizes[engine.current_size_index]
            img.draw_string(10, 35, f"下个正方形: {current_size}x{current_size}", GREEN)
            img.draw_string(10, 55, f"角度: ({engine.gimbal.pan_angle:.1f}, {engine.gimbal.tilt_angle:.1f})", CYAN)

            # 预览正方形
            cx, cy = 160, 120
            preview_size = engine.square_sizes[engine.current_size_index]
            preview_x = cx - preview_size // 4  # 预览时缩小50%显示
            preview_y = cy - preview_size // 4
            preview_w = preview_size // 2
            preview_h = preview_size // 2
            img.draw_rect(preview_x, preview_y, preview_w, preview_h, RED, thickness=2)

            # 状态信息
            current_time = time.time_ms()
            next_draw = 5000 - (current_time % 5000)  # 改为5秒间隔
            img.draw_string(10, 200, f"下次绘制: {next_draw//1000}秒", YELLOW)
            img.draw_string(10, 220, "连续绘制正方形", WHITE)

            # 自动绘制正方形（每5秒）
            if current_time - last_draw_time > 5000:
                img.draw_string(10, 100, "正在绘制正方形...", RED, scale=1.5)
                disp.show(img)
                engine.draw_shape()  # 不需要传参数，总是绘制正方形
                last_draw_time = current_time

            disp.show(img)
            time.sleep_ms(50)

    except Exception as e:
        print(f"错误: {e}")
    finally:
        print("系统退出")

if __name__ == "__main__":
    main()
