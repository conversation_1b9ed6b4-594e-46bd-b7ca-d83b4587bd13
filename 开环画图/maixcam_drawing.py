#!/usr/bin/env python3
"""
MaixCAM 二维云台画图系统 - 精简版
"""
from maix import camera, display, app, time, pwm, pinmap, image
import math

# 颜色定义 - 使用官方API
WHITE = image.Color.from_rgb(255, 255, 255)
RED = image.Color.from_rgb(255, 0, 0)
GREEN = image.Color.from_rgb(0, 255, 0)
YELLOW = image.Color.from_rgb(255, 255, 0)
CYAN = image.Color.from_rgb(0, 255, 255)

# 配置参数
CANVAS_W, CANVAS_H = 320, 240
DRAW_AREA_X, DRAW_AREA_Y = (30, 150), (30, 150)
STEP_DELAY = 80


# 舵机控制
class Gimbal:
    def __init__(self):
        pinmap.set_pin_function("A19", "PWM7")
        pinmap.set_pin_function("A18", "PWM6")
        self.pan = pwm.PWM(7, freq=50, duty=7.5, enable=True)
        self.tilt = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        self.pan_angle = self.tilt_angle = 90

    def set_angle(self, pan_angle, tilt_angle):
        pan_angle = max(0, min(180, pan_angle))
        tilt_angle = max(0, min(180, tilt_angle))
        self.pan.duty(2.5 + 10 * pan_angle / 180)
        self.tilt.duty(2.5 + 10 * tilt_angle / 180)
        self.pan_angle, self.tilt_angle = pan_angle, tilt_angle

    def center(self):
        self.set_angle(90, 90)


# 坐标转换
def pixel_to_angle(x, y):
    scale_x = (DRAW_AREA_X[1] - DRAW_AREA_X[0]) / CANVAS_W
    scale_y = (DRAW_AREA_Y[1] - DRAW_AREA_Y[0]) / CANVAS_H
    servo_x = max(0, min(180, x * scale_x + DRAW_AREA_X[0]))
    servo_y = max(0, min(180, y * scale_y + DRAW_AREA_Y[0]))
    return servo_x, servo_y


# 路径生成
def line_path(p1, p2, steps=20):
    return [(int(p1[0] + i*(p2[0]-p1[0])/steps), int(p1[1] + i*(p2[1]-p1[1])/steps)) for i in range(steps+1)]

def circle_path(cx, cy, r, res=32):
    return [(int(cx + r*math.cos(2*math.pi*i/res)), int(cy + r*math.sin(2*math.pi*i/res))) for i in range(res+1)]

def rect_path(x, y, w, h):
    path = line_path((x,y), (x+w,y), 15)
    path.extend(line_path((x+w,y), (x+w,y+h), 15))
    path.extend(line_path((x+w,y+h), (x,y+h), 15))
    path.extend(line_path((x,y+h), (x,y), 15))
    return path

def triangle_path(cx, cy, size):
    h = size * 0.866
    p1, p2, p3 = (cx, cy-h*0.67), (cx-size/2, cy+h*0.33), (cx+size/2, cy+h*0.33)
    path = line_path(p1, p2, 10)
    path.extend(line_path(p2, p3, 10))
    path.extend(line_path(p3, p1, 10))
    return path

def star_path(cx, cy, r):
    points = []
    for i in range(10):
        a = math.pi * i / 5 - math.pi / 2
        radius = r if i % 2 == 0 else r * 0.4
        points.append((int(cx + radius*math.cos(a)), int(cy + radius*math.sin(a))))
    path = []
    for i in range(10):
        path.extend(line_path(points[i], points[(i+1)%10], 8))
    return path


# 绘图引擎
class DrawingEngine:
    def __init__(self):
        self.gimbal = Gimbal()
        self.shapes = ["直线", "圆形", "矩形", "三角形", "五角星"]

    def draw_shape(self, shape_index):
        paths = [
            line_path((80, 80), (240, 160), 30),
            circle_path(160, 120, 50),
            rect_path(100, 80, 120, 80),
            triangle_path(160, 120, 80),
            star_path(160, 120, 60)
        ]
        if 0 <= shape_index < len(paths):
            print(f"绘制: {self.shapes[shape_index]}")
            self.execute_path(paths[shape_index])

    def execute_path(self, path):
        if not path: return
        print(f"执行路径: {len(path)}点")

        # 移动到起始位置
        start_x, start_y = pixel_to_angle(path[0][0], path[0][1])
        self.gimbal.set_angle(start_x, start_y)
        time.sleep_ms(500)

        # 执行绘制
        for i, (x, y) in enumerate(path):
            if app.need_exit(): break
            servo_x, servo_y = pixel_to_angle(x, y)
            self.gimbal.set_angle(servo_x, servo_y)
            time.sleep_ms(STEP_DELAY)
            if i % 20 == 0:
                print(f"进度: {(i+1)/len(path)*100:.1f}%")

        print("绘制完成")
        time.sleep_ms(1000)
        self.gimbal.center()


# 主程序
def main():
    print("=== MaixCAM 二维云台画图系统 ===")
    try:
        # 使用官方推荐的初始化方式
        cam = camera.Camera(320, 240, image.Format.FMT_RGB888)
        disp = display.Display()
        engine = DrawingEngine()
        engine.gimbal.center()
        time.sleep_ms(2000)

        shape_index, last_draw_time = 0, 0

        while not app.need_exit():
            img = cam.read()

            # 界面显示
            img.draw_string(10, 10, "MaixCAM 画图系统", WHITE, scale=1.5)
            img.draw_string(10, 35, f"当前: {engine.shapes[shape_index]}", GREEN)
            img.draw_string(10, 55, f"角度: ({engine.gimbal.pan_angle:.1f}, {engine.gimbal.tilt_angle:.1f})", CYAN)

            # 预览图形 - 使用官方API格式
            cx, cy = 160, 120
            if shape_index == 0:
                img.draw_line(cx-50, cy-30, cx+50, cy+30, RED, thickness=2)
            elif shape_index == 1:
                img.draw_circle(cx, cy, 40, RED, thickness=2)
            elif shape_index == 2:
                img.draw_rect(cx-40, cy-30, 80, 60, RED, thickness=2)
            elif shape_index == 3:
                h = int(60 * 0.866)
                p1, p2, p3 = (cx, cy-h//2), (cx-30, cy+h//2), (cx+30, cy+h//2)
                img.draw_line(p1[0], p1[1], p2[0], p2[1], RED, thickness=2)
                img.draw_line(p2[0], p2[1], p3[0], p3[1], RED, thickness=2)
                img.draw_line(p3[0], p3[1], p1[0], p1[1], RED, thickness=2)
            elif shape_index == 4:
                img.draw_string(cx-15, cy-10, "★", RED, scale=2)

            # 状态信息
            current_time = time.time_ms()
            next_draw = 8000 - (current_time % 8000)
            img.draw_string(10, 200, f"下次: {next_draw//1000}秒", YELLOW)

            # 自动绘制
            if current_time - last_draw_time > 8000:
                img.draw_string(10, 100, "正在绘制...", RED, scale=1.5)
                disp.show(img)
                engine.draw_shape(shape_index)
                shape_index = (shape_index + 1) % len(engine.shapes)
                last_draw_time = current_time

            disp.show(img)
            time.sleep_ms(50)

    except Exception as e:
        print(f"错误: {e}")
    finally:
        print("系统退出")

if __name__ == "__main__":
    main()
