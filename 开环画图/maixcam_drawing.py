#!/usr/bin/env python3
"""
MaixCAM 二维云台画图系统 - 烧录专用版
精简版本，专为MaixCAM设备优化
"""

from maix import camera, display, app, time, pwm, pinmap, image
import math


# ======================== 配置 ========================
class Config:
    # 画布配置
    CANVAS_WIDTH = 320
    CANVAS_HEIGHT = 240
    
    # 舵机配置
    SERVO_CENTER_X = 90
    SERVO_CENTER_Y = 90
    DRAWING_AREA_X = (30, 150)
    DRAWING_AREA_Y = (30, 150)
    
    # 绘图参数
    STEP_DELAY = 80  # 毫秒
    PATH_DENSITY = 1.5


# ======================== 舵机控制 ========================
class Gimbal:
    def __init__(self):
        # 配置PWM引脚
        pinmap.set_pin_function("A19", "PWM7")  # Pan
        pinmap.set_pin_function("A18", "PWM6")  # Tilt
        
        # 初始化PWM
        self.pan = pwm.PWM(7, freq=50, duty=7.5, enable=True)
        self.tilt = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        
        self.pan_angle = 90
        self.tilt_angle = 90
        
    def set_angle(self, pan_angle, tilt_angle):
        # 限制角度
        pan_angle = max(0, min(180, pan_angle))
        tilt_angle = max(0, min(180, tilt_angle))
        
        # 转换为PWM占空比
        pan_duty = 2.5 + 10 * pan_angle / 180
        tilt_duty = 2.5 + 10 * tilt_angle / 180
        
        self.pan.duty(pan_duty)
        self.tilt.duty(tilt_duty)
        
        self.pan_angle = pan_angle
        self.tilt_angle = tilt_angle
        
    def center(self):
        self.set_angle(90, 90)


# ======================== 坐标转换 ========================
def pixel_to_angle(x, y):
    """像素坐标转舵机角度"""
    # 计算缩放因子
    scale_x = (Config.DRAWING_AREA_X[1] - Config.DRAWING_AREA_X[0]) / Config.CANVAS_WIDTH
    scale_y = (Config.DRAWING_AREA_Y[1] - Config.DRAWING_AREA_Y[0]) / Config.CANVAS_HEIGHT
    
    # 转换坐标
    servo_x = x * scale_x + Config.DRAWING_AREA_X[0]
    servo_y = y * scale_y + Config.DRAWING_AREA_Y[0]
    
    # 限制范围
    servo_x = max(0, min(180, servo_x))
    servo_y = max(0, min(180, servo_y))
    
    return servo_x, servo_y


# ======================== 路径生成 ========================
def interpolate_line(p1, p2, steps):
    """线性插值"""
    points = []
    for i in range(steps + 1):
        t = i / steps
        x = p1[0] + t * (p2[0] - p1[0])
        y = p1[1] + t * (p2[1] - p1[1])
        points.append((int(x), int(y)))
    return points


def generate_circle(center_x, center_y, radius, resolution=32):
    """生成圆形路径"""
    points = []
    for i in range(resolution + 1):
        angle = 2 * math.pi * i / resolution
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        points.append((int(x), int(y)))
    return points


def generate_rectangle(x, y, width, height):
    """生成矩形路径"""
    p1 = (x, y)
    p2 = (x + width, y)
    p3 = (x + width, y + height)
    p4 = (x, y + height)
    
    path = []
    path.extend(interpolate_line(p1, p2, 20))
    path.extend(interpolate_line(p2, p3, 20))
    path.extend(interpolate_line(p3, p4, 20))
    path.extend(interpolate_line(p4, p1, 20))
    
    return path


def generate_triangle(center_x, center_y, size):
    """生成三角形路径"""
    height = size * math.sqrt(3) / 2
    p1 = (center_x, center_y - height * 2/3)
    p2 = (center_x - size/2, center_y + height * 1/3)
    p3 = (center_x + size/2, center_y + height * 1/3)
    
    path = []
    path.extend(interpolate_line(p1, p2, 15))
    path.extend(interpolate_line(p2, p3, 15))
    path.extend(interpolate_line(p3, p1, 15))
    
    return path


def generate_star(center_x, center_y, outer_radius):
    """生成五角星路径"""
    inner_radius = outer_radius * 0.4
    points = []
    
    for i in range(10):
        angle = math.pi * i / 5 - math.pi / 2
        radius = outer_radius if i % 2 == 0 else inner_radius
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        points.append((int(x), int(y)))
    
    path = []
    for i in range(len(points)):
        next_i = (i + 1) % len(points)
        path.extend(interpolate_line(points[i], points[next_i], 10))
    
    return path


# ======================== 绘图引擎 ========================
class DrawingEngine:
    def __init__(self):
        self.gimbal = Gimbal()
        self.shapes = ["直线", "圆形", "矩形", "三角形", "五角星"]
        self.current_shape = 0
        
    def draw_shape(self, shape_index):
        """绘制指定图形"""
        shape_name = self.shapes[shape_index]
        print(f"绘制: {shape_name}")
        
        # 生成路径
        if shape_index == 0:  # 直线
            path = interpolate_line((80, 80), (240, 160), 30)
        elif shape_index == 1:  # 圆形
            path = generate_circle(160, 120, 50)
        elif shape_index == 2:  # 矩形
            path = generate_rectangle(100, 80, 120, 80)
        elif shape_index == 3:  # 三角形
            path = generate_triangle(160, 120, 80)
        elif shape_index == 4:  # 五角星
            path = generate_star(160, 120, 60)
        else:
            return
        
        # 执行绘制
        self.execute_path(path)
        
    def execute_path(self, path):
        """执行绘制路径"""
        if not path:
            return
            
        print(f"执行路径: {len(path)}点")
        
        # 移动到起始位置
        start_x, start_y = pixel_to_angle(path[0][0], path[0][1])
        self.gimbal.set_angle(start_x, start_y)
        time.sleep_ms(500)
        
        # 执行绘制
        for i, (x, y) in enumerate(path):
            if app.need_exit():
                break
                
            servo_x, servo_y = pixel_to_angle(x, y)
            self.gimbal.set_angle(servo_x, servo_y)
            time.sleep_ms(Config.STEP_DELAY)
            
            if i % 20 == 0:
                progress = (i + 1) / len(path) * 100
                print(f"进度: {progress:.1f}%")
        
        print("绘制完成")
        time.sleep_ms(1000)
        self.gimbal.center()


# ======================== 主程序 ========================
def main():
    print("=== MaixCAM 二维云台画图系统 ===")
    
    try:
        # 初始化
        cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
        disp = display.Display()
        engine = DrawingEngine()
        
        # 云台回中
        engine.gimbal.center()
        time.sleep_ms(2000)
        
        shape_index = 0
        last_draw_time = 0
        
        while not app.need_exit():
            # 读取图像
            img = cam.read()
            
            # 绘制界面
            img.draw_string(10, 10, "MaixCAM 画图系统", image.COLOR_WHITE, scale=1.5)
            img.draw_string(10, 35, f"当前图形: {engine.shapes[shape_index]}", image.COLOR_GREEN)
            img.draw_string(10, 55, f"舵机角度: ({engine.gimbal.pan_angle:.1f}, {engine.gimbal.tilt_angle:.1f})", image.COLOR_CYAN)
            
            # 绘制预览
            center_x, center_y = 160, 120
            if shape_index == 0:  # 直线
                img.draw_line(center_x - 50, center_y - 30, center_x + 50, center_y + 30, image.COLOR_RED, 2)
            elif shape_index == 1:  # 圆形
                img.draw_circle(center_x, center_y, 40, image.COLOR_RED, 2)
            elif shape_index == 2:  # 矩形
                img.draw_rect(center_x - 40, center_y - 30, 80, 60, image.COLOR_RED, 2)
            elif shape_index == 3:  # 三角形
                size = 60
                height = size * math.sqrt(3) / 2
                p1 = (center_x, center_y - int(height * 2/3))
                p2 = (center_x - size//2, center_y + int(height * 1/3))
                p3 = (center_x + size//2, center_y + int(height * 1/3))
                img.draw_line(p1[0], p1[1], p2[0], p2[1], image.COLOR_RED, 2)
                img.draw_line(p2[0], p2[1], p3[0], p3[1], image.COLOR_RED, 2)
                img.draw_line(p3[0], p3[1], p1[0], p1[1], image.COLOR_RED, 2)
            elif shape_index == 4:  # 五角星
                img.draw_string(center_x - 15, center_y - 10, "★", image.COLOR_RED, scale=2)
            
            # 状态信息
            current_time = time.time_ms()
            next_draw = 8000 - (current_time % 8000)
            img.draw_string(10, 200, f"下次绘制: {next_draw//1000}秒", image.COLOR_YELLOW)
            img.draw_string(10, 220, "MaixCAM 专用版", image.COLOR_WHITE)

            # 自动绘制（每8秒）
            if current_time - last_draw_time > 8000:
                # 显示绘制状态
                img.draw_string(10, 100, "正在绘制...", image.COLOR_RED, scale=1.5)
                disp.show(img)

                engine.draw_shape(shape_index)
                shape_index = (shape_index + 1) % len(engine.shapes)
                last_draw_time = current_time
            
            # 显示图像
            disp.show(img)
            time.sleep_ms(50)
            
    except Exception as e:
        print(f"错误: {e}")
    finally:
        print("系统退出")


if __name__ == "__main__":
    main()
