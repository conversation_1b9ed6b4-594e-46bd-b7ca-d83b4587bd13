#!/usr/bin/env python3
"""
绘图系统配置模块
提供绘图系统的各种配置参数
"""


class DrawingConfig:
    """绘图系统配置类"""
    def __init__(self):
        # 画布配置
        self.canvas_width = 320          # 画布宽度（像素）
        self.canvas_height = 240         # 画布高度（像素）
        self.canvas_center_x = 160       # 画布中心X坐标
        self.canvas_center_y = 120       # 画布中心Y坐标
        
        # 舵机角度范围配置
        self.servo_range_x = (0, 180)   # X轴舵机角度范围（度）
        self.servo_range_y = (0, 180)   # Y轴舵机角度范围（度）
        self.servo_center_x = 90         # X轴舵机中心角度
        self.servo_center_y = 90         # Y轴舵机中心角度
        
        # 绘图区域映射配置
        self.drawing_area_x = (30, 150)  # 绘图区域X轴角度范围
        self.drawing_area_y = (30, 150)  # 绘图区域Y轴角度范围
        
        # 绘图速度和精度配置
        self.drawing_speed = 1.0         # 绘图速度倍数（1.0为标准速度）
        self.precision_mode = True       # 精度模式开关
        self.step_delay = 50             # 步进延迟（毫秒）
        self.smooth_factor = 0.8         # 平滑因子（0-1）
        
        # 路径规划配置
        self.path_density = 2.0          # 路径点密度（点/像素）
        self.min_path_points = 10        # 最小路径点数量
        self.max_path_points = 1000      # 最大路径点数量
        
        # 坐标转换配置
        self.scale_factor_x = 1.0        # X轴缩放因子
        self.scale_factor_y = 1.0        # Y轴缩放因子
        self.offset_x = 0                # X轴偏移量（像素）
        self.offset_y = 0                # Y轴偏移量（像素）
        
        # 安全配置
        self.enable_boundary_check = True  # 启用边界检查
        self.emergency_stop = False        # 紧急停止标志
        self.max_angle_change = 10         # 最大角度变化量（度/步）


class ShapeConfig:
    """图形绘制配置类"""
    def __init__(self):
        # 直线配置
        self.line_interpolation_steps = 50  # 直线插值步数
        
        # 圆形配置
        self.circle_resolution = 64         # 圆形分辨率（点数）
        self.circle_start_angle = 0         # 圆形起始角度（度）
        
        # 矩形配置
        self.rectangle_corner_smooth = True  # 矩形转角平滑
        self.rectangle_corner_radius = 5     # 转角半径（像素）
        
        # 多边形配置
        self.polygon_close_path = True       # 多边形闭合路径
        self.polygon_smooth_corners = False  # 多边形平滑转角


class UIConfig:
    """用户界面配置类"""
    def __init__(self):
        # 显示配置
        self.display_width = 320         # 显示宽度
        self.display_height = 240        # 显示高度
        self.fps = 30                    # 帧率
        
        # 预览配置
        self.preview_enabled = True      # 启用预览
        self.preview_color = (0, 255, 0) # 预览颜色（BGR）
        self.preview_thickness = 2       # 预览线条粗细
        
        # 状态显示配置
        self.show_coordinates = True     # 显示坐标信息
        self.show_progress = True        # 显示绘图进度
        self.show_fps = False            # 显示帧率
        
        # 交互配置
        self.touch_sensitivity = 10      # 触摸灵敏度
        self.menu_timeout = 5000         # 菜单超时时间（毫秒）


class DebugConfig:
    """调试配置类"""
    def __init__(self):
        # 调试开关
        self.debug_mode = False          # 调试模式
        self.verbose_logging = False     # 详细日志
        self.show_path_points = False    # 显示路径点
        
        # 性能监控
        self.monitor_performance = False # 性能监控
        self.log_servo_commands = False  # 记录舵机命令
        
        # 测试配置
        self.test_mode = False           # 测试模式
        self.simulate_servo = False      # 模拟舵机（用于测试）


# 全局配置实例
DRAWING_CONFIG = DrawingConfig()     # 绘图配置
SHAPE_CONFIG = ShapeConfig()         # 图形配置
UI_CONFIG = UIConfig()               # 界面配置
DEBUG_CONFIG = DebugConfig()         # 调试配置


def get_config():
    """获取绘图配置实例"""
    return DRAWING_CONFIG


def get_shape_config():
    """获取图形配置实例"""
    return SHAPE_CONFIG


def get_ui_config():
    """获取界面配置实例"""
    return UI_CONFIG


def get_debug_config():
    """获取调试配置实例"""
    return DEBUG_CONFIG


def reset_config():
    """重置所有配置为默认值"""
    global DRAWING_CONFIG, SHAPE_CONFIG, UI_CONFIG, DEBUG_CONFIG
    DRAWING_CONFIG = DrawingConfig()
    SHAPE_CONFIG = ShapeConfig()
    UI_CONFIG = UIConfig()
    DEBUG_CONFIG = DebugConfig()


if __name__ == "__main__":
    # 配置测试
    config = get_config()
    print("绘图配置测试:")
    print(f"画布尺寸: {config.canvas_width}x{config.canvas_height}")
    print(f"舵机角度范围: X{config.servo_range_x}, Y{config.servo_range_y}")
    print(f"绘图速度: {config.drawing_speed}")
    print(f"精度模式: {config.precision_mode}")
    print("配置系统初始化完成！")
