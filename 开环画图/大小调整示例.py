#!/usr/bin/env python3
"""
图像大小调整示例代码
演示如何快速调整绘制图像的大小
"""

# ======================== 方法1: 全局绘图区域调整 ========================

# 原始设置（标准大小）
DRAW_AREA_X_STANDARD = (30, 150)  # 120°范围
DRAW_AREA_Y_STANDARD = (30, 150)  # 120°范围

# 小图精密模式（缩小75%）
DRAW_AREA_X_SMALL = (60, 120)     # 60°范围
DRAW_AREA_Y_SMALL = (60, 120)     # 60°范围

# 大图展示模式（放大33%）
DRAW_AREA_X_LARGE = (15, 165)     # 150°范围
DRAW_AREA_Y_LARGE = (15, 165)     # 150°范围

# 超精密模式（缩小90%）
DRAW_AREA_X_MICRO = (80, 100)     # 20°范围
DRAW_AREA_Y_MICRO = (80, 100)     # 20°范围

print("方法1: 全局绘图区域调整")
print(f"标准模式: {DRAW_AREA_X_STANDARD} - 100%大小")
print(f"小图模式: {DRAW_AREA_X_SMALL} - 50%大小")
print(f"大图模式: {DRAW_AREA_X_LARGE} - 125%大小")
print(f"精密模式: {DRAW_AREA_X_MICRO} - 17%大小")
print()

# ======================== 方法2: 图形参数调整 ========================

# 原始图形参数（标准大小）
def get_standard_paths():
    return [
        "line_path((80, 80), (240, 160), 30)",    # 直线
        "circle_path(160, 120, 50)",              # 圆形: 半径50
        "rect_path(100, 80, 120, 80)",            # 矩形: 120x80
        "triangle_path(160, 120, 80)",            # 三角形: 边长80
        "star_path(160, 120, 60)"                 # 五角星: 外径60
    ]

# 缩小版图形参数（50%大小）
def get_small_paths():
    return [
        "line_path((120, 100), (200, 140), 30)",  # 直线缩短
        "circle_path(160, 120, 25)",              # 圆形: 半径25
        "rect_path(130, 100, 60, 40)",            # 矩形: 60x40
        "triangle_path(160, 120, 40)",            # 三角形: 边长40
        "star_path(160, 120, 30)"                 # 五角星: 外径30
    ]

# 放大版图形参数（150%大小）
def get_large_paths():
    return [
        "line_path((40, 40), (280, 200), 30)",    # 直线延长
        "circle_path(160, 120, 75)",              # 圆形: 半径75
        "rect_path(70, 60, 180, 120)",            # 矩形: 180x120
        "triangle_path(160, 120, 120)",           # 三角形: 边长120
        "star_path(160, 120, 90)"                 # 五角星: 外径90
    ]

print("方法2: 图形参数调整")
print("标准大小图形参数:")
for i, path in enumerate(get_standard_paths()):
    print(f"  {i+1}. {path}")

print("\n缩小版图形参数 (50%大小):")
for i, path in enumerate(get_small_paths()):
    print(f"  {i+1}. {path}")

print("\n放大版图形参数 (150%大小):")
for i, path in enumerate(get_large_paths()):
    print(f"  {i+1}. {path}")
print()

# ======================== 方法3: 动态缩放系数 ========================

class ScalableDrawing:
    def __init__(self, scale_factor=1.0):
        self.scale_factor = scale_factor
        
    def set_scale(self, scale):
        """设置缩放比例"""
        self.scale_factor = max(0.1, min(5.0, scale))  # 限制在10%-500%
        print(f"图形大小设置为: {self.scale_factor*100:.0f}%")
        
    def scaled_circle_params(self, cx, cy, r):
        """缩放圆形参数"""
        return cx, cy, r * self.scale_factor
        
    def scaled_rect_params(self, x, y, w, h):
        """缩放矩形参数（保持中心不变）"""
        center_x, center_y = x + w/2, y + h/2
        new_w, new_h = w * self.scale_factor, h * self.scale_factor
        new_x = center_x - new_w/2
        new_y = center_y - new_h/2
        return new_x, new_y, new_w, new_h
        
    def scaled_triangle_params(self, cx, cy, size):
        """缩放三角形参数"""
        return cx, cy, size * self.scale_factor
        
    def scaled_star_params(self, cx, cy, radius):
        """缩放五角星参数"""
        return cx, cy, radius * self.scale_factor

# 使用示例
print("方法3: 动态缩放系数")
drawer = ScalableDrawing()

# 测试不同缩放比例
scales = [0.5, 0.8, 1.0, 1.5, 2.0]
for scale in scales:
    drawer.set_scale(scale)
    cx, cy, r = drawer.scaled_circle_params(160, 120, 50)
    print(f"  缩放{scale*100:.0f}%: 圆形半径 {r:.1f}")

print()

# ======================== 方法4: 预设大小模式 ========================

class SizePresets:
    """预设大小模式"""
    
    MICRO = {
        'name': '超精密模式',
        'draw_area_x': (85, 95),
        'draw_area_y': (85, 95),
        'scale_factor': 0.2,
        'description': '10°范围，适合精密绘图'
    }
    
    SMALL = {
        'name': '小图模式', 
        'draw_area_x': (60, 120),
        'draw_area_y': (60, 120),
        'scale_factor': 0.5,
        'description': '60°范围，适合测试和细节'
    }
    
    STANDARD = {
        'name': '标准模式',
        'draw_area_x': (30, 150),
        'draw_area_y': (30, 150), 
        'scale_factor': 1.0,
        'description': '120°范围，平衡大小和精度'
    }
    
    LARGE = {
        'name': '大图模式',
        'draw_area_x': (15, 165),
        'draw_area_y': (15, 165),
        'scale_factor': 1.5,
        'description': '150°范围，适合展示'
    }
    
    MAXIMUM = {
        'name': '最大模式',
        'draw_area_x': (5, 175),
        'draw_area_y': (5, 175),
        'scale_factor': 2.0,
        'description': '170°范围，最大绘图区域'
    }
    
    @classmethod
    def get_all_presets(cls):
        """获取所有预设模式"""
        return [cls.MICRO, cls.SMALL, cls.STANDARD, cls.LARGE, cls.MAXIMUM]
    
    @classmethod
    def print_presets(cls):
        """打印所有预设模式"""
        print("预设大小模式:")
        for i, preset in enumerate(cls.get_all_presets(), 1):
            range_x = preset['draw_area_x'][1] - preset['draw_area_x'][0]
            print(f"  {i}. {preset['name']}")
            print(f"     角度范围: {range_x}° | 缩放: {preset['scale_factor']*100:.0f}%")
            print(f"     说明: {preset['description']}")

# 显示预设模式
print("方法4: 预设大小模式")
SizePresets.print_presets()
print()

# ======================== 快速调整代码模板 ========================

def quick_size_adjustment():
    """快速大小调整代码模板"""
    
    print("快速调整代码模板:")
    print("=" * 50)
    
    print("# 1. 修改绘图区域（全局缩放）")
    print("DRAW_AREA_X = (60, 120)  # 缩小50%")
    print("DRAW_AREA_Y = (60, 120)  # 缩小50%")
    print()
    
    print("# 2. 修改图形参数（单独调整）")
    print("circle_path(160, 120, 25)     # 圆形半径减半")
    print("rect_path(130, 100, 60, 40)   # 矩形缩小50%")
    print()
    
    print("# 3. 使用缩放系数（程序控制）")
    print("SCALE_FACTOR = 0.7  # 缩小到70%")
    print("scaled_radius = original_radius * SCALE_FACTOR")
    print()
    
    print("# 4. 选择预设模式（一键切换）")
    print("preset = SizePresets.SMALL")
    print("DRAW_AREA_X = preset['draw_area_x']")
    print("DRAW_AREA_Y = preset['draw_area_y']")

quick_size_adjustment()

if __name__ == "__main__":
    print("\n" + "="*60)
    print("图像大小控制示例运行完成！")
    print("请根据需要选择合适的调整方法。")
    print("="*60)
