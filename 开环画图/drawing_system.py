#!/usr/bin/env python3
"""
二维云台画图系统 - 完整集成版
基于MaixPy平台，通过精确控制舵机角度实现物理绘图功能
可直接烧录到MaixCAM设备运行
"""

from maix import camera, display, app, time, pwm, pinmap, image
import cv2
import numpy as np
import math


# ======================== 配置类 ========================
class DrawingConfig:
    """绘图系统配置"""
    def __init__(self):
        # 画布配置
        self.canvas_width = 320
        self.canvas_height = 240
        self.canvas_center_x = 160
        self.canvas_center_y = 120
        
        # 舵机配置
        self.servo_range_x = (0, 180)
        self.servo_range_y = (0, 180)
        self.servo_center_x = 90
        self.servo_center_y = 90
        
        # 绘图区域映射（舵机角度范围）
        self.drawing_area_x = (30, 150)
        self.drawing_area_y = (30, 150)
        
        # 绘图参数
        self.drawing_speed = 1.0
        self.step_delay = 100  # 毫秒
        self.path_density = 2.0


# ======================== 数学工具函数 ========================
class MathUtils:
    """数学工具函数库"""
    
    @staticmethod
    def distance(p1, p2):
        """计算两点距离"""
        return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    @staticmethod
    def linear_interpolate(p1, p2, steps):
        """线性插值生成路径点"""
        points = []
        for i in range(steps + 1):
            t = i / steps
            x = p1[0] + t * (p2[0] - p1[0])
            y = p1[1] + t * (p2[1] - p1[1])
            points.append((int(x), int(y)))
        return points
    
    @staticmethod
    def circle_points(center_x, center_y, radius, resolution=32):
        """生成圆形路径点"""
        points = []
        for i in range(resolution + 1):
            angle = 2 * math.pi * i / resolution
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            points.append((int(x), int(y)))
        return points


# ======================== 坐标转换器 ========================
class CoordinateMapper:
    """坐标转换器"""
    
    def __init__(self, config):
        self.config = config
        
        # 计算缩放因子
        canvas_w = config.canvas_width
        canvas_h = config.canvas_height
        servo_w = config.drawing_area_x[1] - config.drawing_area_x[0]
        servo_h = config.drawing_area_y[1] - config.drawing_area_y[0]
        
        self.scale_x = servo_w / canvas_w
        self.scale_y = servo_h / canvas_h
        
        self.offset_x = config.drawing_area_x[0]
        self.offset_y = config.drawing_area_y[0]
    
    def pixel_to_angle(self, x, y):
        """像素坐标转舵机角度"""
        # 将像素坐标映射到舵机角度
        servo_x = x * self.scale_x + self.offset_x
        servo_y = y * self.scale_y + self.offset_y
        
        # 限制角度范围
        servo_x = max(self.config.servo_range_x[0], 
                     min(self.config.servo_range_x[1], servo_x))
        servo_y = max(self.config.servo_range_y[0], 
                     min(self.config.servo_range_y[1], servo_y))
        
        return servo_x, servo_y
    
    def convert_path(self, path_points):
        """批量转换路径点"""
        servo_path = []
        for x, y in path_points:
            servo_x, servo_y = self.pixel_to_angle(x, y)
            servo_path.append((servo_x, servo_y))
        return servo_path


# ======================== 舵机控制器 ========================
class ServoGimbal:
    """二维云台舵机控制器"""
    
    def __init__(self):
        # 配置PWM引脚
        pinmap.set_pin_function("A19", "PWM7")  # Pan轴
        pinmap.set_pin_function("A18", "PWM6")  # Tilt轴
        
        # 初始化PWM - 50Hz频率，7.5%占空比(90度中位)
        self.pan_servo = pwm.PWM(7, freq=50, duty=7.5, enable=True)
        self.tilt_servo = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        
        # 当前角度
        self.pan_angle = 90
        self.tilt_angle = 90
        
    def set_angle(self, pan_angle, tilt_angle):
        """设置云台角度 (0-180度)"""
        # 角度限制
        pan_angle = max(0, min(180, pan_angle))
        tilt_angle = max(0, min(180, tilt_angle))
        
        # 转换为PWM占空比 (2.5% - 12.5%)
        pan_duty = 2.5 + 10 * pan_angle / 180
        tilt_duty = 2.5 + 10 * tilt_angle / 180
        
        self.pan_servo.duty(pan_duty)
        self.tilt_servo.duty(tilt_duty)
        
        self.pan_angle = pan_angle
        self.tilt_angle = tilt_angle
        
    def center(self):
        """云台回中"""
        self.set_angle(90, 90)
        
    def move_smooth(self, target_pan, target_tilt, steps=10):
        """平滑移动到目标位置"""
        start_pan = self.pan_angle
        start_tilt = self.tilt_angle
        
        for i in range(steps + 1):
            t = i / steps
            current_pan = start_pan + t * (target_pan - start_pan)
            current_tilt = start_tilt + t * (target_tilt - start_tilt)
            
            self.set_angle(current_pan, current_tilt)
            time.sleep_ms(50)


# ======================== 路径规划器 ========================
class PathPlanner:
    """路径规划器"""
    
    def __init__(self, config):
        self.config = config
        self.math_utils = MathUtils()
    
    def plan_line(self, start_point, end_point):
        """规划直线路径"""
        distance = self.math_utils.distance(start_point, end_point)
        steps = max(10, int(distance * self.config.path_density))
        return self.math_utils.linear_interpolate(start_point, end_point, steps)
    
    def plan_circle(self, center_x, center_y, radius):
        """规划圆形路径"""
        resolution = max(16, int(2 * math.pi * radius * self.config.path_density))
        return self.math_utils.circle_points(center_x, center_y, radius, resolution)
    
    def plan_rectangle(self, x, y, width, height):
        """规划矩形路径"""
        # 矩形四个顶点
        p1 = (x, y)
        p2 = (x + width, y)
        p3 = (x + width, y + height)
        p4 = (x, y + height)
        
        # 分别规划四条边
        path = []
        path.extend(self.plan_line(p1, p2))
        path.extend(self.plan_line(p2, p3))
        path.extend(self.plan_line(p3, p4))
        path.extend(self.plan_line(p4, p1))
        
        return path


# ======================== 绘图引擎 ========================
class DrawingEngine:
    """绘图引擎"""
    
    def __init__(self, config):
        self.config = config
        self.gimbal = ServoGimbal()
        self.coordinate_mapper = CoordinateMapper(config)
        self.path_planner = PathPlanner(config)
        
        # 绘图状态
        self.is_drawing = False
        self.current_path = []
        self.path_index = 0
        
    def draw_shape(self, shape_type, parameters):
        """绘制图形"""
        print(f"开始绘制{shape_type}...")
        
        # 生成路径
        if shape_type == "line":
            start, end = parameters
            path = self.path_planner.plan_line(start, end)
        elif shape_type == "circle":
            center_x, center_y, radius = parameters
            path = self.path_planner.plan_circle(center_x, center_y, radius)
        elif shape_type == "rectangle":
            x, y, width, height = parameters
            path = self.path_planner.plan_rectangle(x, y, width, height)
        else:
            print(f"不支持的图形类型: {shape_type}")
            return
        
        # 转换坐标并执行绘制
        servo_path = self.coordinate_mapper.convert_path(path)
        self.execute_path(servo_path)
        
    def execute_path(self, servo_path):
        """执行舵机路径"""
        if not servo_path:
            return
            
        print(f"执行路径，共{len(servo_path)}个点")
        self.is_drawing = True
        
        # 移动到起始位置
        start_x, start_y = servo_path[0]
        self.gimbal.move_smooth(start_x, start_y)
        time.sleep_ms(500)
        
        # 执行绘制路径
        for i, (x, y) in enumerate(servo_path):
            if app.need_exit():
                break
                
            self.gimbal.set_angle(x, y)
            time.sleep_ms(self.config.step_delay)
            
            # 显示进度
            if i % 10 == 0:
                progress = (i + 1) / len(servo_path) * 100
                print(f"绘制进度: {progress:.1f}%")
        
        self.is_drawing = False
        print("绘制完成！")
        
        # 回到中心位置
        time.sleep_ms(500)
        self.gimbal.center()


# ======================== 用户界面 ========================
class DrawingUI:
    """绘图用户界面"""
    
    def __init__(self, drawing_engine):
        self.engine = drawing_engine
        self.camera = camera.Camera(320, 240, image.Format.FMT_BGR888)
        self.display = display.Display()
        
        # UI状态
        self.current_mode = "menu"  # menu, drawing
        self.selected_shape = 0     # 当前选择的图形
        self.shapes = ["直线", "圆形", "矩形"]
        
    def draw_menu(self, img):
        """绘制菜单界面"""
        # 标题
        img.draw_string(10, 10, "二维云台画图系统", image.COLOR_WHITE, scale=1.5)
        img.draw_string(10, 35, "选择要绘制的图形:", image.COLOR_GREEN)
        
        # 图形选项
        for i, shape in enumerate(self.shapes):
            color = image.COLOR_RED if i == self.selected_shape else image.COLOR_WHITE
            img.draw_string(20, 60 + i * 25, f"{i+1}. {shape}", color)
        
        # 操作提示
        img.draw_string(10, 160, "操作说明:", image.COLOR_YELLOW)
        img.draw_string(10, 180, "触摸屏幕选择图形", image.COLOR_WHITE)
        img.draw_string(10, 200, "长按开始绘制", image.COLOR_WHITE)
        
    def draw_preview(self, img, shape_type):
        """绘制预览图形"""
        center_x, center_y = 160, 120
        
        if shape_type == "直线":
            img.draw_line(center_x - 50, center_y - 30, 
                         center_x + 50, center_y + 30, 
                         image.COLOR_GREEN, thickness=2)
        elif shape_type == "圆形":
            img.draw_circle(center_x, center_y, 40, image.COLOR_GREEN, thickness=2)
        elif shape_type == "矩形":
            img.draw_rect(center_x - 40, center_y - 30, 80, 60, 
                         image.COLOR_GREEN, thickness=2)
    
    def handle_touch(self, touch_y):
        """处理触摸事件"""
        if 60 <= touch_y <= 135:  # 图形选择区域
            shape_index = (touch_y - 60) // 25
            if 0 <= shape_index < len(self.shapes):
                self.selected_shape = shape_index
                return True
        return False
    
    def start_drawing(self):
        """开始绘制选中的图形"""
        shape_name = self.shapes[self.selected_shape]
        print(f"开始绘制: {shape_name}")
        
        # 预定义的图形参数
        if shape_name == "直线":
            self.engine.draw_shape("line", ((80, 80), (240, 160)))
        elif shape_name == "圆形":
            self.engine.draw_shape("circle", (160, 120, 50))
        elif shape_name == "矩形":
            self.engine.draw_shape("rectangle", (100, 80, 120, 80))
    
    def run(self):
        """运行用户界面"""
        print("=== 二维云台画图系统启动 ===")
        
        # 云台回中
        self.engine.gimbal.center()
        time.sleep_ms(1000)
        
        touch_start_time = 0
        long_press_threshold = 1000  # 长按阈值(毫秒)
        
        while not app.need_exit():
            # 读取图像
            img = self.camera.read()
            
            # 绘制界面
            if self.current_mode == "menu":
                self.draw_menu(img)
                self.draw_preview(img, self.shapes[self.selected_shape])
                
                # 简单的触摸检测（这里用时间模拟）
                current_time = time.time_ms()
                
                # 自动演示模式：循环绘制不同图形
                demo_cycle = (current_time // 8000) % len(self.shapes)  # 每8秒切换图形
                if demo_cycle != self.selected_shape:
                    self.selected_shape = demo_cycle

                # 自动开始绘制（每8秒执行一次）
                if current_time % 8000 < 100:
                    self.current_mode = "drawing"
                    self.start_drawing()
                    
            elif self.current_mode == "drawing":
                # 绘制状态显示
                img.draw_string(10, 10, "正在绘制中...", image.COLOR_RED, scale=1.5)
                img.draw_string(10, 40, f"图形: {self.shapes[self.selected_shape]}", image.COLOR_WHITE)
                
                if not self.engine.is_drawing:
                    self.current_mode = "menu"
                    time.sleep_ms(2000)  # 显示完成信息2秒
            
            # 显示图像
            self.display.show(img)
            time.sleep_ms(50)


# ======================== 主程序 ========================
def main():
    """主程序入口"""
    print("初始化二维云台画图系统...")
    
    try:
        # 创建配置
        config = DrawingConfig()
        
        # 创建绘图引擎
        drawing_engine = DrawingEngine(config)
        
        # 创建用户界面
        ui = DrawingUI(drawing_engine)
        
        # 运行系统
        ui.run()
        
    except Exception as e:
        print(f"系统错误: {e}")
    finally:
        print("系统退出")


if __name__ == "__main__":
    main()
