# MaixCAM 二维云台画图系统 - 烧录说明

## 🎯 快速开始

### 推荐文件（烧录专用）
```
maixcam_drawing.py  ← 推荐使用此文件
```

这是专为MaixCAM优化的精简版本，包含完整功能且性能最佳。

### 备选文件
```
drawing_system.py   ← 完整功能版本
main.py            ← 启动器
```

## 📋 烧录步骤

1. **准备文件**
   - 将 `maixcam_drawing.py` 复制到MaixCAM设备
   - 或者使用MaixVision IDE直接运行

2. **硬件连接**
   ```
   Pan轴舵机  → A19引脚 (PWM7)
   Tilt轴舵机 → A18引脚 (PWM6)
   舵机电源  → 5V (2A以上)
   舵机地线  → GND
   ```

3. **运行程序**
   ```bash
   python maixcam_drawing.py
   ```

## ⚙️ 系统配置

### 关键参数（可调整）
```python
# 绘图区域（舵机角度范围）
DRAWING_AREA_X = (30, 150)  # X轴角度范围
DRAWING_AREA_Y = (30, 150)  # Y轴角度范围

# 绘图速度
STEP_DELAY = 80  # 毫秒（越小越快）

# 路径密度
PATH_DENSITY = 1.5  # 路径点密度
```

## 🎨 支持的图形

1. **直线** - 对角线绘制
2. **圆形** - 标准圆形
3. **矩形** - 四边形绘制
4. **三角形** - 等边三角形
5. **五角星** - 五角星图案

## 🔧 工作模式

- **自动演示模式**：每8秒自动切换图形
- **实时预览**：屏幕显示当前图形预览
- **状态显示**：舵机角度、倒计时等信息
- **平滑运动**：路径插值确保运动平滑

## 📊 性能特点

- **内存优化**：精简代码，适合嵌入式设备
- **实时性**：50ms刷新率，流畅显示
- **稳定性**：异常处理，自动恢复
- **扩展性**：易于添加新图形

## 🛠️ 故障排除

### 舵机不动
- 检查5V电源是否充足
- 确认引脚连接正确
- 验证PWM信号输出

### 绘图偏移
- 调整 `DRAWING_AREA_X/Y` 参数
- 检查舵机机械限位
- 校准坐标映射

### 运动不平滑
- 增加 `STEP_DELAY` 值
- 提高 `PATH_DENSITY` 参数
- 检查舵机负载

## 📈 扩展功能

### 添加新图形
```python
def generate_custom_shape(center_x, center_y, size):
    # 自定义图形生成逻辑
    points = []
    # ... 生成路径点
    return points
```

### 调整绘图参数
```python
# 在Config类中修改
STEP_DELAY = 60      # 更快的绘图速度
PATH_DENSITY = 2.0   # 更高的路径密度
```

## 🔍 调试模式

在代码中添加调试信息：
```python
print(f"当前角度: ({servo_x:.1f}, {servo_y:.1f})")
print(f"路径点数: {len(path)}")
```

## 📝 版本信息

- **版本**: v1.0.0
- **平台**: MaixCAM / MaixPy
- **文件大小**: ~8KB
- **内存占用**: <1MB
- **支持图形**: 5种基础图形

## ⚠️ 注意事项

1. **电源要求**：确保5V 2A以上电源
2. **机械限位**：避免舵机过度转动
3. **散热**：长时间运行注意散热
4. **安全**：使用画笔时注意安全

---

**快速烧录命令**：
```bash
# 直接运行
python maixcam_drawing.py

# 或使用MaixVision IDE
# 1. 打开maixcam_drawing.py
# 2. 点击运行按钮
# 3. 观察绘图效果
```
