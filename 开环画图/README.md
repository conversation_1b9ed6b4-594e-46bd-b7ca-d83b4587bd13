# MaixCAM 二维云台画图系统

## 🎯 一键运行

```bash
python maixcam_drawing.py
```

## 📋 硬件连接

```
Pan轴舵机  → A19引脚 (PWM7)
Tilt轴舵机 → A18引脚 (PWM6)
舵机电源  → 5V (2A+)
```

## 🎨 支持图形

- 直线、圆形、矩形、三角形、五角星
- 自动演示：每8秒切换图形
- 实时预览和状态显示

## ⚙️ 参数调整

```python
DRAW_AREA_X = (30, 150)  # X轴角度范围
DRAW_AREA_Y = (30, 150)  # Y轴角度范围  
STEP_DELAY = 80          # 绘图速度(毫秒)
```

## 📊 特点

- **超精简**：仅181行代码
- **零依赖**：只需MaixPy
- **即插即用**：直接烧录运行
- **高效稳定**：优化算法，流畅绘图

---
**文件大小**: 5.8KB | **内存占用**: <500KB | **支持图形**: 5种
