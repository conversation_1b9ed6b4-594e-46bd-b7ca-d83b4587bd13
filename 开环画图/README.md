# 二维云台画图系统

基于MaixPy平台的二维云台画图系统，通过精确控制舵机角度实现物理绘图功能。

## 功能特点

- ✅ **多种图形支持**：直线、圆形、矩形、三角形、五角星
- ✅ **精确控制**：PWM舵机控制，角度精度高
- ✅ **实时预览**：屏幕显示当前绘制图形
- ✅ **自动演示**：循环绘制不同图形
- ✅ **平滑运动**：路径插值和平滑控制
- ✅ **状态显示**：实时显示舵机角度和绘制进度

## 硬件要求

- MaixCAM 开发板
- 2个舵机（Pan轴和Tilt轴）
- 画笔或激光器（可选）

## 接线说明

- Pan轴舵机：连接到 A19 引脚 (PWM7)
- Tilt轴舵机：连接到 A18 引脚 (PWM6)
- 舵机电源：5V供电
- 舵机地线：共地

## 使用方法

### 方法1：运行主程序
```bash
python main.py
```

### 方法2：直接运行完整系统
```bash
python drawing_system.py
```

## 系统配置

可以在 `DrawingConfig` 类中修改以下参数：

- `canvas_width/height`：画布尺寸
- `servo_range_x/y`：舵机角度范围
- `drawing_area_x/y`：绘图区域映射
- `drawing_speed`：绘图速度
- `step_delay`：步进延迟

## 支持的图形

1. **直线**：两点间直线
2. **圆形**：指定圆心和半径
3. **矩形**：指定位置和尺寸
4. **三角形**：等边三角形
5. **五角星**：五角星图案

## 工作模式

系统运行在自动演示模式下：
- 每8秒自动切换图形类型
- 自动执行绘制过程
- 实时显示绘制进度和舵机状态

## 文件结构

```
开环画图/
├── drawing_system.py    # 完整集成系统
├── main.py             # 主启动文件
├── README.md           # 说明文档
└── config/             # 配置文件目录
    ├── __init__.py
    └── drawing_config.py
```

## 技术特点

- **模块化设计**：清晰的组件分离
- **坐标转换**：像素坐标到舵机角度的精确映射
- **路径规划**：智能路径生成和优化
- **平滑控制**：避免舵机突然跳变
- **实时反馈**：状态显示和进度跟踪

## 注意事项

1. 确保舵机供电充足（5V 2A以上）
2. 检查舵机连接和引脚配置
3. 调整绘图区域避免机械限位
4. 根据实际硬件调整速度参数

## 故障排除

- **舵机不动**：检查电源和引脚连接
- **绘图偏移**：调整坐标映射参数
- **运动不平滑**：增加路径插值点数
- **角度超限**：检查绘图区域设置

## 扩展功能

系统支持以下扩展：
- 添加新的图形类型
- 自定义绘图参数
- 集成触摸控制
- 添加文字绘制功能

---

**开发团队**：Drawing System Team  
**版本**：v1.0.0  
**平台**：MaixPy / MaixCAM
