"""
二维云台跟踪系统配置文件
用户可以根据实际硬件和需求调整这些参数
"""

# 摄像头配置
CAMERA_CONFIG = {
    'width': 640,
    'height': 480,
    'fps': 30
}

# ROI学习区域配置
ROI_CONFIG = {
    'width': 100,
    'height': 100,
    'auto_center': True  # 自动居中
}

# 颜色检测配置
COLOR_CONFIG = {
    'min_area': 500,        # 最小检测面积
    'min_pixels': 500,      # 最小像素数
    'threshold_multiplier': 2.0  # 阈值扩展倍数
}

# 云台硬件配置
GIMBAL_CONFIG = {
    'pan_pin': "A19",       # 水平舵机引脚
    'tilt_pin': "A18",      # 垂直舵机引脚
    'pan_center': 90,       # 水平中心角度
    'tilt_center': 90,      # 垂直中心角度
    'angle_limits': {
        'pan_min': 30,      # 水平最小角度
        'pan_max': 150,     # 水平最大角度
        'tilt_min': 60,     # 垂直最小角度
        'tilt_max': 120     # 垂直最大角度
    }
}

# PID控制参数
PID_CONFIG = {
    'pan': {
        'kp': 0.8,          # 比例系数
        'ki': 0.1,          # 积分系数
        'kd': 0.2,          # 微分系数
        'output_limits': (-20, 20)  # 输出限制
    },
    'tilt': {
        'kp': 0.8,
        'ki': 0.1, 
        'kd': 0.2,
        'output_limits': (-20, 20)
    }
}

# 系统配置
SYSTEM_CONFIG = {
    'control_frequency': 50,    # 控制频率 (Hz)
    'learning_timeout': 10,     # 学习模式超时 (秒)
    'tracking_timeout': 5,      # 跟踪丢失超时 (秒)
    'debug_mode': True          # 调试模式
}

# 显示配置
DISPLAY_CONFIG = {
    'show_roi': True,           # 显示ROI框
    'show_crosshair': True,     # 显示中心十字线
    'show_info': True,          # 显示信息文字
    'font_scale': 1.0           # 字体缩放
}

# 预设颜色阈值 (LAB色彩空间)
PRESET_COLORS = {
    'red': [[0, 80, 20, 127, 15, 127]],
    'green': [[0, 80, -128, -10, 0, 30]], 
    'blue': [[0, 80, 0, 40, -128, -20]],
    'yellow': [[0, 80, -10, 10, 20, 127]],
    'orange': [[0, 80, 10, 50, 20, 80]]
}