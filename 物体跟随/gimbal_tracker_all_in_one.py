#!/usr/bin/env python3
"""
二维云台物体跟随系统 - 完整集成版
基于颜色学习的物体跟踪与云台控制

功能特性:
1. ROI区域颜色学习
2. 实时物体检测与跟踪  
3. PID控制的平滑云台跟随
4. 完整的参数配置系统
5. 一体化部署，无需额外配置文件

硬件连接:
- 水平舵机: A19 引脚 (PWM7)
- 垂直舵机: A18 引脚 (PWM6)
- 舵机供电: 5V + GND

使用方法:
1. 运行程序: python gimbal_tracker_all_in_one.py
2. 将目标物体放在绿色ROI框内
3. 等待自动学习颜色(默认10秒)
4. 系统自动切换到跟踪模式

作者: Alex (MaixPy Team)
版本: v1.0
"""

from maix import camera, display, image, app, time, pwm, pinmap
import math

# ================================
# 配置参数区域 - 用户可根据需要修改
# ================================

# 摄像头配置
CAMERA_WIDTH = 640
CAMERA_HEIGHT = 480
CAMERA_FPS = 30

# ROI学习区域配置
ROI_WIDTH = 100
ROI_HEIGHT = 100

# 颜色检测配置
MIN_AREA = 500          # 最小检测面积
MIN_PIXELS = 500        # 最小像素数
THRESHOLD_MULTIPLIER = 2.0  # 阈值扩展倍数

# 云台硬件配置
PAN_PIN = "A19"         # 水平舵机引脚
TILT_PIN = "A18"        # 垂直舵机引脚
PAN_CENTER = 90         # 水平中心角度
TILT_CENTER = 90        # 垂直中心角度

# 云台角度限制
PAN_MIN = 30
PAN_MAX = 150
TILT_MIN = 60
TILT_MAX = 120

# PID控制参数
PAN_KP = 0.8            # 水平比例系数
PAN_KI = 0.1            # 水平积分系数
PAN_KD = 0.2            # 水平微分系数
TILT_KP = 0.8           # 垂直比例系数
TILT_KI = 0.1           # 垂直积分系数
TILT_KD = 0.2           # 垂直微分系数

# 系统配置
CONTROL_FREQUENCY = 50  # 控制频率 (Hz)
LEARNING_TIMEOUT = 10   # 学习模式超时 (秒)
DEBUG_MODE = True       # 调试模式

# 显示配置
SHOW_CROSSHAIR = True   # 显示中心十字线
SHOW_INFO = True        # 显示信息文字
FONT_SCALE = 1.0        # 字体缩放

# 预设颜色阈值 (LAB色彩空间) - 可选使用
PRESET_COLORS = {
    'red': [[0, 80, 20, 127, 15, 127]],
    'green': [[0, 80, -128, -10, 0, 30]], 
    'blue': [[0, 80, 0, 40, -128, -20]],
    'yellow': [[0, 80, -10, 10, 20, 127]],
    'orange': [[0, 80, 10, 50, 20, 80]]
}

# ================================
# 核心功能类定义
# ================================

class PIDController:
    """PID控制器 - 用于平滑的云台控制"""
    
    def __init__(self, kp=0.5, ki=0.1, kd=0.05, output_limits=(-30, 30)):
        self.kp = kp  # 比例系数
        self.ki = ki  # 积分系数  
        self.kd = kd  # 微分系数
        self.output_limits = output_limits  # 输出限制
        
        self.last_error = 0
        self.integral = 0
        
    def update(self, current_value, target_value):
        """更新PID控制器"""
        error = target_value - current_value
        
        # 积分项
        self.integral += error
        # 积分限幅
        self.integral = max(-100, min(100, self.integral))
        
        # 微分项
        derivative = error - self.last_error
        
        # PID输出
        output = (self.kp * error + 
                 self.ki * self.integral + 
                 self.kd * derivative)
        
        # 输出限幅
        output = max(self.output_limits[0], min(self.output_limits[1], output))
        
        self.last_error = error
        return output
        
    def reset(self):
        """重置PID控制器"""
        self.last_error = 0
        self.integral = 0

class ServoGimbal:
    """二维云台控制器"""
    
    def __init__(self, pan_pin=PAN_PIN, tilt_pin=TILT_PIN):
        # 配置PWM引脚
        pinmap.set_pin_function(pan_pin, "PWM7")
        pinmap.set_pin_function(tilt_pin, "PWM6") 
        
        # 初始化PWM - 50Hz频率，7.5%占空比(90度中位)
        self.pan_servo = pwm.PWM(7, freq=50, duty=7.5, enable=True)
        self.tilt_servo = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        
        # 当前角度
        self.pan_angle = PAN_CENTER
        self.tilt_angle = TILT_CENTER
        
        if DEBUG_MODE:
            print(f"云台初始化完成 - Pan: {pan_pin}, Tilt: {tilt_pin}")
        
    def set_angle(self, pan_angle, tilt_angle):
        """设置云台角度 (0-180度)"""
        # 角度限制
        pan_angle = max(PAN_MIN, min(PAN_MAX, pan_angle))
        tilt_angle = max(TILT_MIN, min(TILT_MAX, tilt_angle))
        
        # 转换为PWM占空比 (2.5% - 12.5%)
        pan_duty = 2.5 + 10 * pan_angle / 180
        tilt_duty = 2.5 + 10 * tilt_angle / 180
        
        self.pan_servo.duty(pan_duty)
        self.tilt_servo.duty(tilt_duty)
        
        self.pan_angle = pan_angle
        self.tilt_angle = tilt_angle
        
    def center(self):
        """云台回中"""
        self.set_angle(PAN_CENTER, TILT_CENTER)
        if DEBUG_MODE:
            print("云台回中完成")

class ColorLearner:
    """颜色学习器 - 用于ROI区域的颜色阈值学习"""
    
    def __init__(self, roi_size=(ROI_WIDTH, ROI_HEIGHT)):
        self.roi_size = roi_size
        self.roi_x = 0
        self.roi_y = 0
        self.learned_threshold = None
        
    def set_roi_center(self, img_width, img_height):
        """设置ROI到图像中心"""
        self.roi_x = (img_width - self.roi_size[0]) // 2
        self.roi_y = (img_height - self.roi_size[1]) // 2
        
    def draw_roi(self, img):
        """在图像上绘制ROI区域"""
        # 绘制ROI框
        img.draw_rect(self.roi_x, self.roi_y, 
                     self.roi_size[0], self.roi_size[1], 
                     image.COLOR_GREEN, thickness=2)
        
        # 绘制中心十字线
        center_x = self.roi_x + self.roi_size[0] // 2
        center_y = self.roi_y + self.roi_size[1] // 2
        img.draw_line(center_x - 10, center_y, center_x + 10, center_y, image.COLOR_GREEN)
        img.draw_line(center_x, center_y - 10, center_x, center_y + 10, image.COLOR_GREEN)
        
        # 显示提示文字
        if SHOW_INFO:
            img.draw_string(10, 10, "ROI Learning Mode", image.COLOR_WHITE, scale=FONT_SCALE)
            img.draw_string(10, 30, "Place target in green box", image.COLOR_WHITE, scale=FONT_SCALE)
        
    def learn_color_from_roi(self, img):
        """从ROI区域学习颜色阈值"""
        # 提取ROI区域
        roi_img = img.crop(self.roi_x, self.roi_y, self.roi_size[0], self.roi_size[1])

        # 直接获取LAB统计信息 (MaixPy图像默认支持LAB统计)
        # 使用全范围阈值来获取整个ROI的统计信息
        full_threshold = [[0, 100, -128, 127, -128, 127]]
        stats = roi_img.get_statistics(full_threshold)

        # 计算阈值范围 (使用标准差扩展范围)
        l_mean, a_mean, b_mean = stats.l_mean(), stats.a_mean(), stats.b_mean()
        l_std, a_std, b_std = stats.l_std_dev(), stats.a_std_dev(), stats.b_std_dev()

        # 设置阈值范围 (均值 ± THRESHOLD_MULTIPLIER*标准差)
        threshold = [
            int(max(0, l_mean - THRESHOLD_MULTIPLIER * l_std)),    # L_min
            int(min(100, l_mean + THRESHOLD_MULTIPLIER * l_std)),  # L_max
            int(max(-128, a_mean - THRESHOLD_MULTIPLIER * a_std)), # A_min
            int(min(127, a_mean + THRESHOLD_MULTIPLIER * a_std)),  # A_max
            int(max(-128, b_mean - THRESHOLD_MULTIPLIER * b_std)), # B_min
            int(min(127, b_mean + THRESHOLD_MULTIPLIER * b_std))   # B_max
        ]

        self.learned_threshold = [threshold]

        if DEBUG_MODE:
            print(f"颜色学习完成!")
            print(f"LAB均值: L={l_mean:.1f}, A={a_mean:.1f}, B={b_mean:.1f}")
            print(f"LAB标准差: L={l_std:.1f}, A={a_std:.1f}, B={b_std:.1f}")
            print(f"学习阈值: {threshold}")

        return True
        
    def get_threshold(self):
        """获取学习到的颜色阈值"""
        return self.learned_threshold

class ObjectTracker:
    """物体跟踪器 - 基于颜色的blob检测"""
    
    def __init__(self, threshold, min_area=MIN_AREA, min_pixels=MIN_PIXELS):
        self.threshold = threshold
        self.min_area = min_area
        self.min_pixels = min_pixels
        self.last_target = None
        self.lost_count = 0
        
    def find_target(self, img):
        """查找目标物体"""
        if not self.threshold:
            return None
            
        # 查找颜色blob
        blobs = img.find_blobs(self.threshold,
                              area_threshold=self.min_area,
                              pixels_threshold=self.min_pixels)

        if not blobs:
            self.lost_count += 1
            return None

        # 选择最大的blob作为目标
        target_blob = max(blobs, key=lambda b: b.area())

        # 计算目标中心点
        target_center = (target_blob.cx(), target_blob.cy())

        # 绘制检测结果
        self.draw_target(img, target_blob)

        self.last_target = target_center
        self.lost_count = 0
        return target_center
        
    def draw_target(self, img, blob):
        """绘制检测到的目标"""
        # 绘制边界框
        corners = blob.corners()
        for i in range(4):
            img.draw_line(corners[i][0], corners[i][1], 
                         corners[(i + 1) % 4][0], corners[(i + 1) % 4][1], 
                         image.COLOR_RED, thickness=2)
        
        # 绘制中心点
        img.draw_circle(blob.cx(), blob.cy(), 8, image.COLOR_RED, -1)
        
        # 显示信息
        if SHOW_INFO:
            info = f"Area: {blob.area()}"
            img.draw_string(blob.x(), max(0, blob.y() - 20), info, image.COLOR_RED, scale=FONT_SCALE)

class GimbalTracker:
    """云台跟踪系统主控制器"""
    
    def __init__(self):
        print("=== 二维云台跟踪系统启动 ===")
        
        # 初始化组件
        print("初始化摄像头...")
        self.camera = camera.Camera(CAMERA_WIDTH, CAMERA_HEIGHT)
        
        print("初始化显示屏...")
        self.display = display.Display()
        
        print("初始化云台...")
        self.gimbal = ServoGimbal()
        
        print("初始化颜色学习器...")
        self.color_learner = ColorLearner()
        self.tracker = None
        
        # PID控制器
        self.pan_pid = PIDController(kp=PAN_KP, ki=PAN_KI, kd=PAN_KD, output_limits=(-20, 20))
        self.tilt_pid = PIDController(kp=TILT_KP, ki=TILT_KI, kd=TILT_KD, output_limits=(-20, 20))
        
        # 状态管理
        self.mode = "learning"  # learning / tracking
        self.img_center = (CAMERA_WIDTH // 2, CAMERA_HEIGHT // 2)
        self.start_time = time.time_ms()
        
        # 设置ROI位置
        self.color_learner.set_roi_center(CAMERA_WIDTH, CAMERA_HEIGHT)
        
        # 云台回中
        self.gimbal.center()
        
        print("系统初始化完成!")
        print(f"图像分辨率: {CAMERA_WIDTH}x{CAMERA_HEIGHT}")
        print(f"ROI大小: {ROI_WIDTH}x{ROI_HEIGHT}")
        print(f"学习超时: {LEARNING_TIMEOUT}秒")
        
    def run(self):
        """主运行循环"""
        print("\n开始运行...")
        print("请将目标物体放在绿色ROI框内进行颜色学习")
        
        frame_count = 0
        last_fps_time = time.time_ms()
        
        while not app.need_exit():
            # 获取图像
            img = self.camera.read()
            current_time = time.time_ms()

            if self.mode == "learning":
                self._learning_mode(img, current_time)
            elif self.mode == "tracking":
                self._tracking_mode(img)

            # 绘制中心十字线
            if SHOW_CROSSHAIR:
                self._draw_crosshair(img)

            # 显示FPS (每秒更新一次)
            frame_count += 1
            if current_time - last_fps_time >= 1000:
                fps = frame_count * 1000 / (current_time - last_fps_time)
                if DEBUG_MODE and SHOW_INFO:
                    img.draw_string(CAMERA_WIDTH - 100, 10, f"FPS: {fps:.1f}", image.COLOR_YELLOW, scale=FONT_SCALE)
                frame_count = 0
                last_fps_time = current_time

            # 显示图像
            self.display.show(img)

            # 控制帧率
            time.sleep_ms(1000 // CONTROL_FREQUENCY)
                
        print("系统退出")
        
    def _learning_mode(self, img, current_time):
        """学习模式处理"""
        # 绘制ROI
        self.color_learner.draw_roi(img)
        
        # 显示倒计时
        if SHOW_INFO:
            elapsed = (current_time - self.start_time) / 1000
            remaining = max(0, LEARNING_TIMEOUT - elapsed)
            img.draw_string(10, 50, f"Auto learn in: {remaining:.1f}s", image.COLOR_WHITE, scale=FONT_SCALE)
        
        # 自动学习颜色
        if current_time - self.start_time > LEARNING_TIMEOUT * 1000:
            if not self.color_learner.learned_threshold:
                if self.color_learner.learn_color_from_roi(img):
                    # 创建跟踪器
                    threshold = self.color_learner.get_threshold()
                    self.tracker = ObjectTracker(threshold)
                    self.mode = "tracking"
                    print("颜色学习完成，切换到跟踪模式")
                else:
                    # 学习失败，重新开始
                    self.start_time = time.time_ms()
                    print("颜色学习失败，重新开始...")
                
    def _tracking_mode(self, img):
        """跟踪模式处理"""
        # 查找目标
        target = self.tracker.find_target(img)
        
        if target:
            # 计算误差
            error_x = target[0] - self.img_center[0]
            error_y = target[1] - self.img_center[1]
            
            # PID控制计算
            pan_adjustment = self.pan_pid.update(target[0], self.img_center[0])
            tilt_adjustment = self.tilt_pid.update(target[1], self.img_center[1])
            
            # 更新云台角度
            new_pan = self.gimbal.pan_angle - pan_adjustment * 0.1
            new_tilt = self.gimbal.tilt_angle + tilt_adjustment * 0.1
            
            self.gimbal.set_angle(new_pan, new_tilt)
            
            # 显示跟踪信息
            if SHOW_INFO:
                img.draw_string(10, 10, "Tracking Mode", image.COLOR_GREEN, scale=FONT_SCALE)
                img.draw_string(10, 30, f"Target: ({target[0]}, {target[1]})", image.COLOR_GREEN, scale=FONT_SCALE)
                img.draw_string(10, 50, f"Error: ({error_x:.1f}, {error_y:.1f})", image.COLOR_GREEN, scale=FONT_SCALE)
                img.draw_string(10, 70, f"Gimbal: ({self.gimbal.pan_angle:.1f}, {self.gimbal.tilt_angle:.1f})", image.COLOR_GREEN, scale=FONT_SCALE)
        else:
            # 目标丢失
            if SHOW_INFO:
                img.draw_string(10, 10, "Target Lost", image.COLOR_RED, scale=FONT_SCALE)
                img.draw_string(10, 30, f"Lost frames: {self.tracker.lost_count}", image.COLOR_RED, scale=FONT_SCALE)
            
            # 重置PID
            if self.tracker.lost_count > 30:  # 丢失超过30帧重置PID
                self.pan_pid.reset()
                self.tilt_pid.reset()
                
    def _draw_crosshair(self, img):
        """绘制中心十字线"""
        center_x, center_y = self.img_center
        line_len = 20
        
        img.draw_line(center_x - line_len, center_y, 
                     center_x + line_len, center_y, 
                     image.COLOR_BLUE, thickness=2)
        img.draw_line(center_x, center_y - line_len, 
                     center_x, center_y + line_len, 
                     image.COLOR_BLUE, thickness=2)

# ================================
# 主程序入口
# ================================

def main():
    """主函数"""
    print("=" * 50)
    print("二维云台物体跟随系统 v1.0")
    print("基于MaixPy的智能跟踪系统")
    print("=" * 50)
    
    # 显示配置信息
    if DEBUG_MODE:
        print(f"\n当前配置:")
        print(f"- 摄像头: {CAMERA_WIDTH}x{CAMERA_HEIGHT}@{CAMERA_FPS}fps")
        print(f"- ROI大小: {ROI_WIDTH}x{ROI_HEIGHT}")
        print(f"- 云台引脚: Pan={PAN_PIN}, Tilt={TILT_PIN}")
        print(f"- 角度范围: Pan={PAN_MIN}-{PAN_MAX}°, Tilt={TILT_MIN}-{TILT_MAX}°")
        print(f"- PID参数: Pan(P={PAN_KP},I={PAN_KI},D={PAN_KD}), Tilt(P={TILT_KP},I={TILT_KI},D={TILT_KD})")
        print(f"- 控制频率: {CONTROL_FREQUENCY}Hz")
        print(f"- 学习超时: {LEARNING_TIMEOUT}秒")
    
    # 创建并运行跟踪器
    tracker = GimbalTracker()
    tracker.run()
    print("程序结束")

if __name__ == "__main__":
    main()