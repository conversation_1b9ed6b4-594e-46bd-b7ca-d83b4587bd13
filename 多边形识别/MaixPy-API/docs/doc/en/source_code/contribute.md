---
title: Contributing to MaixCAM MaixPy Documentation Modification and Code Contribution
---

## Contributing to MaixPy Documentation Modification

* Click the "Edit this page" button in the top right corner of the documentation you want to modify to enter the GitHub source documentation page.
* Make sure you are logged in to your GitHub account.
* Click the pencil icon in the top right corner of the GitHub preview documentation page to modify the content.
* GitHub will prompt you to fork a copy to your own repository. Click the "Fork" button.
> This step forks the MaixPy source code repository to your own account, allowing you to freely modify it.
* Modify the documentation content, then fill in the modification description at the bottom of the page, and click "Commit changes".
* Then find the "Pull requests" button in your repository and click to create a new Pull request.
* In the pop-up page, fill in the modification description and click "Submit Pull request". Others and administrators can then see your modifications on the [Pull requests page](https://github.com/sipeed/MaixPy/pulls).
* Wait for the administrator to review and approve, and your modifications will be merged into the MaixPy source code repository.
* After the merge is successful, the documentation will be automatically updated to the [MaixPy official documentation](https://wiki.sipeed.com/maixpy).
> Due to CDN caching, it may take some time to see the update. For urgent updates, you can contact the administrator for manual refreshing.
> You can also visit [en.wiki.sipeed.com/maixpy](https://en.wiki.sipeed.com/maixpy) to view the GitHub Pages service version, which is updated in real-time without caching.

## Contributing to MaixPy Code Contribution

* Visit the MaixPy code repository address: [github.com/sipeed/MaixPy](https://github.com/sipeed/MaixPy)
* Before modifying the code, it is best to create an [issue](https://github.com/sipeed/MaixPy/issues) first, describing the content you want to modify to let others know your ideas and plans, so that everyone can participate in the modification discussion and avoid duplication of effort.
* Click the "Fork" button in the top right corner to fork a copy of the MaixPy code repository to your own account.
* Then clone a copy of the code from your account to your local machine.
* After modifying the code, commit it to your repository.
* Then find the "Pull requests" button in your repository and click to create a new Pull request.
* In the pop-up page, fill in the modification description and click "Submit Pull request". Others and administrators can then see your modifications on the [Pull requests page](https://github.com/sipeed/MaixPy/pulls).
* Wait for the administrator to review and approve, and your modifications will be merged into the MaixPy source code repository.

> Note that most of the MaixPy code is automatically generated from [MaixCDK](https://github.com/sipeed/MaixCDK), so if you modify the C/C++ source code, you may need to modify this repository first.
