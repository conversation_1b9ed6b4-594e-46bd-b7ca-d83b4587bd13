---
title: MaixPy bm8653 Driver Instructions
update:
  - date: 2024-08-27
    author: iawak9lkm
    version: 1.0.0
    content: Initial document
---

## Introduction to BM8653

BM8653 is a real-time clock (RTC) chip widely used in various electronic devices to provide accurate time and date information. It features low power consumption and high precision, capable of continuing to operate via a backup battery when the device is powered off, ensuring the continuity and accuracy of time.

## Using BM8653 in MaixPy

Using BM8653 in MaixPy is straightforward; you only need to know which I2C bus your platform's BM8653 is mounted on. The onboard BM8563 on the MaixCAM Pro is mounted on I2C-4.

Example:

```python
from maix import ext_dev, pinmap, err, time

### Enable I2C
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SCL")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SDA")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)

BM8653_I2CBUS_NUM = 4

rtc = ext_dev.bm8563.BM8563(BM8653_I2CBUS_NUM)

### 2020-12-31 23:59:45
t = [2020, 12, 31, 23, 59, 45]

# Set time
rtc.datetime(t)

while True:
    rtc_now = rtc.datetime()
    print(f"{rtc_now[0]}-{rtc_now[1]}-{rtc_now[2]} {rtc_now[3]}:{rtc_now[4]}:{rtc_now[5]}")
    time.sleep(1)
```
If you are using the onboard BM8653 on the MaixCAM Pro, there is no need to enable I2C-4.

The example demonstrates reading from and writing to the BM8653, setting or retrieving the current time.

You can also use the following example to set the current time in the BM8653 to the system time, or set the current system time to the time in the BM8653.

```python
from maix import ext_dev, pinmap, err, time

### Enable I2C
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SCL")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SDA")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)


BM8653_I2CBUS_NUM = 4

rtc = ext_dev.bm8563.BM8563(BM8653_I2CBUS_NUM)

### Update RTC time from system
rtc.systohc()

### Update system time from RTC
# rtc.hctosys()

while True:
    rtc_now = rtc.datetime()
    print(f"{rtc_now[0]}-{rtc_now[1]}-{rtc_now[2]} {rtc_now[3]}:{rtc_now[4]}:{rtc_now[5]}")
    time.sleep(1)
```

**The underlying implementation of BM8653 is similar to the singleton pattern, ensuring that read and write operations on a single BM8653 are thread-safe. This means you can create BM8653 objects freely and read/write to BM8653 from any location without causing data race conditions.**

The timetuple passed to the BM8653 object follows the format (year, month, day[, hour[, minute[, second]]]), meaning the first three parameters are mandatory, and any missing subsequent parameters will not modify the corresponding time. BM8653 guarantees that a returned timetuple being empty indicates an error, and if not empty, it will always contain a list of 6 elements: (year, month, day, hour, minute, second).

For detailed information on the BM8653 API, please refer to the [BM8653 API Documentation](../../../api/maix/ext_dev/bm8563.md)

