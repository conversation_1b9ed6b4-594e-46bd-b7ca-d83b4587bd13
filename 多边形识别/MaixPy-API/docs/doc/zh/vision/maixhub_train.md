---
title: MaixCAM MaixPy 使用 MaixHub 在线训练 AI 模型给 MaixPy 使用
update:
  - date: 2024-04-03
    author: neucrack
    version: 1.0.0
    content: 初版文档
---

## 简介

MaixHub 提供了在线训练 AI 模型的功能，可以直接在浏览器中训练模型，不需要购买昂贵的机器，不需要搭建复杂的开发环境，也不需要写代码，非常适合入门，也适合懒得翻代码的老手。

## 使用 MaixHub 训练模型的基本步骤


### 确认要识别的数据类型和模型类型

要训练一个 AI 模型，需要先确定是什么数据和模型，目前 MaixHub（2024.4）提供了图像数据的`物体分类模型`和`物体检测模型`，都是图像识别模型， `物体分类模型` 比 `物体检测模型` 更简单，因为物体检测需要标注物体在图中的位置，会比较麻烦，物体分类则只需要给出图像中是什么，不需要坐标，所以更简单， 如果是初学者建议先从物体分类开始。


### 采集数据

如前面的 AI 基础所说，要训练模型，必须准备训练用的数据集让 AI 学习，对于图像训练，我们需要创建一个数据集，并且上传图片到数据集。

保证设备已经连接网络（WiFi）。
打开设备上的 MaixHub 应用选择 采集数据 来拍照并一键上传到 MaixHub。需要先在 MaixHub 创建数据集，然后点击 设备 上传数据，会出现一个 二维码，设备扫描二维码来与MaixHub 建立连接。

注意要分清训练集和验证集的区别，要想实机运行的效果和训练效果相当，验证集的数据一定要和实机运行拍摄的图像质量一样，训练集也建议用设备拍摄的，如果要用网上的图片，一定只能用在训练集，不要用在验证集，因为数据量小，数据集与实机运行越接近越好。

### 标注数据

对于分类模型，在上传的时候就顺便已经标注好了，即上传时选择好了图片属于那个分类。

对于目标检测模型，上传完成后需要进行手动标注，即在每一张图中框出要被识别物体的坐标大小和分类。
这个标注过程你也可以选择自己在自己的电脑中离线用比如 labelimg 这样的软件标注完毕后使用数据集中的导入功能导入到 MaixHub。
标注时善用快捷键标注起来会更快，后面MaixHub 也会增加更多辅助标注和自动标注工具（目前在上传视频处有自动标注工具也可以尝试使用）。


### 训练模型

选择训练参数训练，选择对应的设备平台，选择 maixcam，等待排队训练，可以实时看到训练进度，等待完成即可。

### 部署模型

训练完成后，可以设备的 MaixHub 应用中选择 部署 功能，扫码进行部署。
设备开会自动下载模型并且运行起来，模型会被存在本地，后面也能选择再次运行。

如果你觉得识别效果很不错，可以一键分享到模型库让更多人使用。



## 使用方法

请到 [MaixHub](https://maixhub.com) 注册账号，然后登录，主页有视频教程，学习即可。

注意教程如果是使用了 M2dock 这个开发板，和 MaixCAM也是类似的，只是设备（板子）上使用的 MaixHub 应用可能稍微有点区别，大体上是相同的，请注意举一反三。




