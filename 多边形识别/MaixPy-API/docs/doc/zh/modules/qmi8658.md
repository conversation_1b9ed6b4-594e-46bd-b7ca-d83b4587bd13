---
title: MaixPy qmi8658驱动说明
update:
  - date: 2024-08-27
    author: iawak9lkm
    version: 1.0.0
    content: 初版文档
---

## QMI8658 简介

QMI8658是一款集成了三轴陀螺仪和三轴加速度计的惯性测量单元（IMU）芯片. 它能够提供高精度的姿态、运动和位置数据，适用于各种需要精确运动检测的应用场景，如无人机、机器人、游戏控制器和虚拟现实设备等。QMI8658具有低功耗、高稳定性和高灵敏度的特点.

## MaixPy 中使用 QMI8658

在 MaixPy 中使用 QMI8658 很简单, 您只需要知道您使用的平台上 QMI8658 挂载在哪个 I2C 总线上. MaixCAM Pro 板载的 QMI8658 挂载在 I2C-4 上.

示例代码:

```python
from maix import ext_dev, pinmap, err, time

### Enable I2C
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SCL")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SDA")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)

QMI8658_I2CBUS_NUM = 4

imu = ext_dev.qmi8658.QMI8658(QMI8658_I2CBUS_NUM,
                              mode=ext_dev.imu.Mode.DUAL,
                              acc_scale=ext_dev.imu.AccScale.ACC_SCALE_2G,
                              acc_odr=ext_dev.imu.AccOdr.ACC_ODR_8000,
                              gyro_scale=ext_dev.imu.GyroScale.GYRO_SCALE_16DPS,
                              gyro_odr=ext_dev.imu.GyroOdr.GYRO_ODR_8000)

while True:
    data = imu.read()
    print("\n-------------------")
    print(f"acc x: {data[0]}")
    print(f"acc y: {data[1]}")
    print(f"acc z: {data[2]}")
    print(f"gyro x: {data[3]}")
    print(f"gyro y: {data[4]}")
    print(f"gyro z: {data[5]}")
    print(f"temp: {data[6]}")
    print("-------------------\n")
```

按照您的需求初始化 QMI8658 对象, 然后调用 `read()` 即可. `read()` 返回的是从 QMI8658 中读出的原始数据.

**如果 `mode` 参数选择 `DUAL`, 则 `read()`返回的数据为 `[acc_x, acc_y, acc_z, gyro_x, gyro_y, gyro_z, temp]`, 如果 `mode` 只选择 ACC/GYRO 中的一个, 只会返回对应的 `[x, y, z, temp]`, 例如选择 ACC, `read()` 会返回 `[acc_x, acc_y, acc_z, temp]`.**

有关 QMI8658 API 的详细说明请看 [QMI8658 API 文档](../../../api/maix/ext_dev/qmi8658.md)