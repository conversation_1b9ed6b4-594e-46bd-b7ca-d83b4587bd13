{"site_name": "MaixPy", "site_slogon": "", "site_root_url": "/maixpy/", "site_domain": "wiki.sipeed.com", "site_protocol": "https", "config_template_dir": "config", "source": "https://github.com/sipeed/MaixPy/blob/main/docs", "route": {"docs": {"/api/": "api", "/doc/zh/": "doc/zh"}, "pages": {"/": "pages/index"}, "assets": {"/static/": "static", "/doc/assets/": "doc/assets"}}, "translate": {"docs": {"/doc/zh/": [{"url": "/doc/en/", "src": "doc/en"}]}, "pages": {"/": [{"url": "/en/", "src": "pages/index_en"}]}}, "plugins": {"teedoc-plugin-markdown-parser": {"from": "pypi", "config": {}}, "teedoc-plugin-theme-default": {"from": "pypi", "config": {"dark": true, "show_print_page": false, "env": {"main_color": "#c33d45"}}}, "teedoc-plugin-search": {"from": "pypi", "config": {"search_hint": "Search"}}, "teedoc-plugin-assets": {"from": "pypi", "config": {"header_items": ["/static/css/custom.css"], "footer_items": ["/static/js/custom.js"], "env": {"main_color": "#c33d45", "second_color": "#922f36"}}}, "teedoc-plugin-baidu-tongji": {"from": "pypi", "config": {"code": "9cb07365544a53067c56c346c838181a"}}, "teedoc-plugin-thumbs-up": {"from": "pypi", "config": {"url": "https://thumbs-up.sipeed.com", "show_up_count": true, "show_down_count": false}}}, "rebuild_changes_delay": 1, "robots": {"User-agent": "*"}, "layout_root_dir": "layout"}