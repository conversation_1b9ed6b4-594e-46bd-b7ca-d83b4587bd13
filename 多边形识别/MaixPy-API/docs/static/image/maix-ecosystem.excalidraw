{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 149, "versionNonce": 170295430, "isDeleted": false, "id": "lgDrdWXT9bbyk2trMN6Vw", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 542, "y": 545, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 602.8055468764685, "height": 133, "seed": 1518581314, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false}, {"type": "rectangle", "version": 171, "versionNonce": 1910780634, "isDeleted": false, "id": "yN5xUSW8oMw5XX3EP_QN5", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 543.8055468764685, "y": 480.0277343823424, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 602.388906247063, "height": 64, "seed": 1257741826, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false}, {"type": "rectangle", "version": 196, "versionNonce": 869302214, "isDeleted": false, "id": "pZsOD-erMl2GhwgdB3len", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 544.3303130110232, "y": 415.05640610609106, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 602.388906247063, "height": 64, "seed": 471371202, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false}, {"type": "rectangle", "version": 110, "versionNonce": 256690074, "isDeleted": false, "id": "-bPjeuUMFo6K5trDKlsX1", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 541.0280468294778, "y": 263.3917182712827, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 604.1601573513434, "height": 150.63726589960157, "seed": 1124435330, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false}, {"type": "rectangle", "version": 140, "versionNonce": 1853396742, "isDeleted": false, "id": "lfgD3TSEibNF4UEOT65O6", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 822.1639067169694, "y": 263.3917182712827, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 323.0242974638518, "height": 116.80429708792639, "seed": 956138818, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "jFsobJvvk3oLsXpIMZZGR"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 53, "versionNonce": 1145357402, "isDeleted": false, "id": "jFsobJvvk3oLsXpIMZZGR", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 876.139831083661, "y": 291.7938668152459, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 215.07244873046875, "height": 60, "seed": 1960559838, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "MaixPy\nPython API\n(Auto Sync with MaixCDK）", "textAlign": "center", "verticalAlign": "middle", "containerId": "lfgD3TSEibNF4UEOT65O6", "originalText": "MaixPy\nPython API\n(Auto Sync with MaixCDK）", "lineHeight": 1.25, "baseline": 55}, {"type": "rectangle", "version": 148, "versionNonce": 580634182, "isDeleted": false, "id": "4EvFUYMCaR0VTnX-qZHxz", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 957.7240489190394, "y": -159.09379322492532, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 186.8868753406823, "height": 420.0688708668026, "seed": 2002750722, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "87m8Zy2thgUpCwI3aLGLU"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 88, "versionNonce": 983509274, "isDeleted": false, "id": "87m8Zy2thgUpCwI3aLGLU", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 962.9272827319587, "y": 0.9406422084759924, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 176.48040771484375, "height": 100, "seed": 322530910, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "MaixVision Workstation\n\npython coding, camera\npreview, graphic coding,\ncamera monitor...", "textAlign": "center", "verticalAlign": "middle", "containerId": "4EvFUYMCaR0VTnX-qZHxz", "originalText": "MaixVision Workstation\n\npython coding, camera preview, graphic coding, camera monitor...", "lineHeight": 1.25, "baseline": 95}, {"type": "rectangle", "version": 304, "versionNonce": 555158918, "isDeleted": false, "id": "EcrNFNI6yL92AoSlIWVVO", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 544.3798973239758, "y": -160.84555157963533, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 413.2455476283189, "height": 297.55670053920767, "seed": 1940561090, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false}, {"type": "rectangle", "version": 411, "versionNonce": 1775171034, "isDeleted": false, "id": "rRCFAQjwm1vnYBnizXNgV", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 645.073256882533, "y": -152.95838497869576, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 152.7585529775803, "height": 137.38091739682395, "seed": 461223042, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "IljY5UxdaUGFHT9sh3TVh"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 206, "versionNonce": 388459718, "isDeleted": false, "id": "IljY5UxdaUGFHT9sh3TVh", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 659.1804157565771, "y": -104.26792628028379, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 124.54423522949219, "height": 40, "seed": 1150609118, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "AI model online \ntraining", "textAlign": "center", "verticalAlign": "middle", "containerId": "rRCFAQjwm1vnYBnizXNgV", "originalText": "AI model online training", "lineHeight": 1.25, "baseline": 35}, {"type": "rectangle", "version": 429, "versionNonce": 1659316890, "isDeleted": false, "id": "-s8ub4VAaSqSgcxn_ClB6", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 806.195982956134, "y": -152.1965882262943, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 143.40089934047262, "height": 135.41277270964167, "seed": 1352127554, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "HiKrFzJa43Cm5SmFjQ47Z"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 209, "versionNonce": 40494086, "isDeleted": false, "id": "HiKrFzJa43Cm5SmFjQ47Z", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 813.1922929168977, "y": -94.49020187147346, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 129.4082794189453, "height": 20, "seed": 1527204318, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "AI model sharing", "textAlign": "center", "verticalAlign": "middle", "containerId": "-s8ub4VAaSqSgcxn_ClB6", "originalText": "AI model sharing", "lineHeight": 1.25, "baseline": 15}, {"type": "rectangle", "version": 471, "versionNonce": 479905626, "isDeleted": false, "id": "4LZjGT8mwEbDX3LZWxl6R", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 645.8346979642795, "y": -8.461871689459826, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 151.86479451166826, "height": 139.42752107389416, "seed": 1691876354, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "K0Wr4IrqdbZ8pYoLS5o9U"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 213, "versionNonce": 618361670, "isDeleted": false, "id": "K0Wr4IrqdbZ8pYoLS5o9U", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 656.4469429373987, "y": 41.25188884748725, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 130.6403045654297, "height": 40, "seed": 1106354562, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "Project sharing, \nreward", "textAlign": "center", "verticalAlign": "middle", "containerId": "4LZjGT8mwEbDX3LZWxl6R", "originalText": "Project sharing, reward", "lineHeight": 1.25, "baseline": 35}, {"type": "rectangle", "version": 477, "versionNonce": 1060949018, "isDeleted": false, "id": "fawTMpzGHBvMo4rpWSaai", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 806.6026053001843, "y": -8.271965644919078, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 143.81518326443873, "height": 139.21006740019186, "seed": 373107650, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "znJrxwJJU0EAYq6Tw-t9R"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 249, "versionNonce": 2027494022, "isDeleted": false, "id": "znJrxwJJU0EAYq6Tw-t9R", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 837.0541117273256, "y": 51.33306805517685, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 82.91217041015625, "height": 20, "seed": 1660884994, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "APP Store", "textAlign": "center", "verticalAlign": "middle", "containerId": "fawTMpzGHBvMo4rpWSaai", "originalText": "APP Store", "lineHeight": 1.25, "baseline": 15}, {"type": "rectangle", "version": 103, "versionNonce": 1891645658, "isDeleted": false, "id": "AD9gq4p6sVlK5uCSSdF79", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 541.7062882439832, "y": 137.47954767793942, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 414.3818235520964, "height": 125.37029693455148, "seed": 182870914, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false}, {"type": "rectangle", "version": 129, "versionNonce": 622844358, "isDeleted": false, "id": "rBVBpnjw-mw4TFaxBVqob", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 546.9896785754999, "y": 143.80647502552387, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 156.74454974378438, "height": 114.3227462361117, "seed": 1293067074, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "N9U-XXCh_1lmG6TFdyWZf"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 59, "versionNonce": 548933018, "isDeleted": false, "id": "N9U-XXCh_1lmG6TFdyWZf", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 583.3218686085248, "y": 190.96784814357972, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 84.08016967773438, "height": 20, "seed": 1628866946, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "User APPs", "textAlign": "center", "verticalAlign": "middle", "containerId": "rBVBpnjw-mw4TFaxBVqob", "originalText": "User APPs", "lineHeight": 1.25, "baseline": 15}, {"type": "rectangle", "version": 227, "versionNonce": 1892462854, "isDeleted": false, "id": "taBSuFoogLth7z8TAIGfA", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 711.7353054438793, "y": 143.5381217253858, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 238.5851579676612, "height": 113.2492266066432, "seed": 1193339650, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "zbtofl7zMyang1nAhL_EV"}], "updated": 1705903395747, "link": null, "locked": false}, {"type": "text", "version": 154, "versionNonce": 1415006810, "isDeleted": false, "id": "zbtofl7zMyang1nAhL_EV", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 717.9876241127685, "y": 150.1627350287074, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 226.0805206298828, "height": 100, "seed": 756599262, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "Official APPs：\nLauncher, APP store, \nSettings,Camera, Monitor, AI\ndetector, line tracer, QR \ncode finder etc.", "textAlign": "center", "verticalAlign": "middle", "containerId": "taBSuFoogLth7z8TAIGfA", "originalText": "Official APPs：\nLauncher, APP store, Settings,Camera, Monitor, AI detector, line tracer, QR code finder etc.", "lineHeight": 1.25, "baseline": 95}, {"type": "text", "version": 49, "versionNonce": 928817222, "isDeleted": false, "id": "sFPgg4fqvH6SkVMAcU2Ic", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 609.1081303162628, "y": 321.1711479479845, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 86.400146484375, "height": 40, "seed": 1066416834, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "MaixCDK\nC/C++ API", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "MaixCDK\nC/C++ API", "lineHeight": 1.25, "baseline": 34}, {"type": "text", "version": 60, "versionNonce": 1920003866, "isDeleted": false, "id": "mdOMOmkUcecRL2pFLwKdr", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 797.665729771511, "y": 435.4203338248098, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 55.5521240234375, "height": 20, "seed": 1426035330, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "Drivers", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Drivers", "lineHeight": 1.25, "baseline": 14}, {"type": "text", "version": 79, "versionNonce": 1554818950, "isDeleted": false, "id": "HEf5jPBhfnk4ha0pwN7__", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 778.159771207175, "y": 502.2979060453904, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 104.52821350097656, "height": 20, "seed": 2049831490, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "System(Linux)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "System(Linux)", "lineHeight": 1.25, "baseline": 14}, {"type": "text", "version": 101, "versionNonce": 259521498, "isDeleted": false, "id": "huzNuYRp7_Zxwnq1MvOpN", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 798.594584941241, "y": 584.0371609816557, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 66.52815246582031, "height": 20, "seed": 1912232450, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "MaixCam", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "MaixCam", "lineHeight": 1.25, "baseline": 14}, {"type": "text", "version": 78, "versionNonce": 1090111174, "isDeleted": false, "id": "BNhQWU8ZdE9UUh9PGz8WY", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 729.8593023811998, "y": 614.0371609816557, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 248.54452514648438, "height": 20, "seed": 1264830914, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "For STEM, Industry, Personal...", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "For STEM, Industry, Personal...", "lineHeight": 1.25, "baseline": 14}, {"type": "text", "version": 75, "versionNonce": 964653210, "isDeleted": false, "id": "DsZ8iDV3d86Jb4hh_bEIW", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 567.7901931261797, "y": -20.78670585983116, "strokeColor": "#343a40", "backgroundColor": "transparent", "width": 61.456146240234375, "height": 20, "seed": 1569276290, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705903395747, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "MaixHub", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "MaixHub", "lineHeight": 1.25, "baseline": 14}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}