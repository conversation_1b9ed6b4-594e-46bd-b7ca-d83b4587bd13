from maix import image, display, app, time, camera, gpio, pinmap, uart
import cv2
import numpy as np
import struct

# 初始化显示屏对象
disp = display.Display()
# 初始化摄像头对象，设置图像分辨率和格式
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 配置闪光灯（照明LED）- B3引脚
pinmap.set_pin_function("B3", "GPIOB3")
flash_led = gpio.GPIO("GPIOB3", gpio.Mode.OUT)
# 开启闪光灯并保持常亮
flash_led.value(1)


# 配置串口通信
# 方法1: 使用默认串口 UART0 (/dev/ttyS0)
try:
    serial = uart.UART("/dev/ttyS0", 115200)
    print("串口UART0初始化成功")
except Exception as e:
    print(f"串口UART0初始化失败: {e}")
    serial = None

# 方法2: 使用UART1 (需要配置引脚映射)
# pinmap.set_pin_function("A18", "UART1_RX")  # 配置A18为UART1接收引脚
# pinmap.set_pin_function("A19", "UART1_TX")  # 配置A19为UART1发送引脚
# try:
#     serial1 = uart.UART("/dev/ttyS1", 115200)
#     print("串口UART1初始化成功")
# except Exception as e:
#     print(f"串口UART1初始化失败: {e}")
#     serial1 = None

# 定义棋盘的最小面积限制，用于筛选有效的棋盘轮廓
MIN_BOARD_AREA = 5000

# LAB色彩空间棋子识别阈值参数
# 白棋LAB阈值 (L: 亮度, A: 绿红轴, B: 蓝黄轴)
WHITE_LAB_MIN = np.array([70, 120, 120])   # 白棋LAB最小值
WHITE_LAB_MAX = np.array([255, 135, 135])  # 白棋LAB最大值

# 黑棋LAB阈值
BLACK_LAB_MIN = np.array([0, 120, 120])    # 黑棋LAB最小值
BLACK_LAB_MAX = np.array([50, 135, 135])   # 黑棋LAB最大值

SAMPLE_RADIUS = 15         # 采样半径
MIN_PIECE_PIXELS = 50      # 最小棋子像素数量

# ROI区域参数
ROI_WIDTH = 30             # ROI区域宽度
ROI_HEIGHT = 20            # ROI区域高度
MIN_BLOB_AREA = 100        # 最小色块面积

# 串口通信函数
def send_board_data(board, left_pieces, right_pieces):
    """
    发送棋盘数据到串口
    数据格式: 帧头(2字节) + 数据长度(1字节) + 棋盘数据(9字节) + 左侧数据(4字节) + 右侧数据(4字节) + 校验和(1字节) + 帧尾(1字节)
    """
    if serial is None:
        return
    
    try:
        # 构建数据包
        frame_header = b'\xAA\xBB'  # 帧头
        
        # 棋盘数据 (3x3 = 9字节)
        board_data = b''
        for row in board:
            for cell in row:
                board_data += struct.pack('B', cell)
        
        # 左侧放置区数据 (4字节)
        left_data = b''
        for piece in left_pieces:
            left_data += struct.pack('B', piece)
        
        # 右侧放置区数据 (4字节)
        right_data = b''
        for piece in right_pieces:
            right_data += struct.pack('B', piece)
        
        # 数据长度
        data_length = len(board_data) + len(left_data) + len(right_data)
        length_byte = struct.pack('B', data_length)
        
        # 计算校验和 (简单异或校验)
        checksum = 0
        for byte in board_data + left_data + right_data:
            checksum ^= byte
        checksum_byte = struct.pack('B', checksum)
        
        # 帧尾
        frame_tail = b'\xFF'
        
        # 组装完整数据包
        packet = frame_header + length_byte + board_data + left_data + right_data + checksum_byte + frame_tail
        
        # 发送数据
        serial.write(packet)
        print(f"串口发送数据: {packet.hex()}")
        
    except Exception as e:
        print(f"串口发送失败: {e}")

def send_text_data(board, left_pieces, right_pieces):
    """
    发送文本格式的棋盘数据到串口
    """
    if serial is None:
        return
    
    try:
        # 构建文本数据
        board_str = ""
        for row in board:
            board_str += ''.join(map(str, row)) + "|"
        
        left_str = ''.join(map(str, left_pieces))
        right_str = ''.join(map(str, right_pieces))
        
        # 格式: $BOARD:000102|010201|202010|LEFT:0120|RIGHT:2010*
        message = f"$BOARD:{board_str}LEFT:{left_str}|RIGHT:{right_str}*\r\n"
        
        # 发送文本数据
        serial.write_str(message)
        print(f"串口发送文本: {message.strip()}")
        
    except Exception as e:
        print(f"串口文本发送失败: {e}")

def read_serial_data():
    """
    读取串口数据
    """
    if serial is None:
        return None
    
    try:
        data = serial.read(timeout=10)  # 10ms超时
        if data:
            print(f"串口接收数据: {data}")
            return data
    except Exception as e:
        print(f"串口读取失败: {e}")
    
    return None

# 主循环，持续运行直到应用需要退出
while not app.need_exit():
    # 从摄像头读取一帧图像
    img = cam.read()

    # 记录开始时间
    t = time.ticks_ms()
    # 将 maix.image.Image 对象转换为 numpy.ndarray 对象
    img = image.image2cv(img, ensure_bgr=False, copy=False)
    # 计算转换所需的时间并打印
    print("time: ", time.ticks_ms() - t)

    # 将图像转换为LAB色彩空间
    lab_img = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    
    # 将图像转换为灰度图像
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # 对灰度图像进行高斯模糊，减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    # 使用 Canny 边缘检测算法检测图像的边缘
    edged = cv2.Canny(blurred, 50, 150)

    # 查找图像中的轮廓
    contours, _ = cv2.findContours(edged.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    # 初始化标志位，表示是否找到有效的棋盘
    valid_board = False
    # 初始化最大轮廓对象
    max_contour = None
    
    # 查找最大的轮廓作为棋盘
    if contours:
        # 遍历所有轮廓
        for contour in contours:
            # 计算当前轮廓的面积
            area = cv2.contourArea(contour)
            # 如果轮廓面积大于最小面积限制
            if area > MIN_BOARD_AREA:
                # 如果最大轮廓对象为空或者当前轮廓面积更大
                if max_contour is None or area > cv2.contourArea(max_contour):
                    # 更新最大轮廓对象
                    max_contour = contour

        if max_contour is not None:
            # 计算最大轮廓的周长
            perimeter = cv2.arcLength(max_contour, True)
            # 对最大轮廓进行多边形逼近，得到近似的四边形
            approx = cv2.approxPolyDP(max_contour, 0.02 * perimeter, True)

            # 判断近似的多边形是否为四边形
            if len(approx) == 4:
                # 标记找到有效的棋盘
                valid_board = True
                # 获取四边形的四个顶点坐标
                pts = approx.reshape(4, 2)
                
                # 计算顶点坐标的和
                s = pts.sum(axis=1)
                # 创建一个形状为 (4, 2) 的零数组，用于存储排序后的顶点坐标
                rect = np.zeros((4, 2), dtype="float32")
                # 左上角顶点的坐标和最小，将其赋值给 rect[0]
                rect[0] = pts[np.argmin(s)]
                # 右下角顶点的坐标和最大，将其赋值给 rect[2]
                rect[2] = pts[np.argmax(s)]
                # 计算每个顶点坐标的差值
                diff = np.diff(pts, axis=1)
                # 右上角顶点的坐标差值最小，将其赋值给 rect[1]
                rect[1] = pts[np.argmin(diff)]
                # 左下角顶点的坐标差值最大，将其赋值给 rect[3]
                rect[3] = pts[np.argmax(diff)]
                
                # 解包排序后的顶点坐标
                (tl, tr, br, bl) = rect
                # 定义目标棋盘的宽度和高度
                width = 300
                height = 300
                # 定义目标棋盘的四个顶点坐标
                dst_rect = np.array([
                    [0, 0],
                    [width, 0],
                    [width, height],
                    [0, height]
                ], dtype="float32")

                # 检查四边形的边长是否大于 0，确保是有效的四边形
                if np.linalg.norm(tl - tr) > 0 and np.linalg.norm(tr - br) > 0 and np.linalg.norm(br - bl) > 0 and np.linalg.norm(bl - tl) > 0:
                    # 计算透视变换矩阵，用于将原始四边形变换到目标矩形
                    M = cv2.getPerspectiveTransform(rect, dst_rect)
                    # 初始化存储棋格中心点坐标的列表
                    grid_centers = []

                    # 遍历 3x3 的棋格
                    for i in range(3):
                        for j in range(3):
                            # 计算当前棋格中心点在目标矩形中的 x 坐标
                            cx = int((j + 0.5) * (width / 3))
                            # 计算当前棋格中心点在目标矩形中的 y 坐标
                            cy = int((i + 0.5) * (height / 3))
                            # 将棋格中心点坐标添加到列表中
                            grid_centers.append((cx, cy))

                    # 计算透视变换矩阵的逆矩阵，用于将目标矩形中的点变换回原始图像
                    inv_M = np.linalg.inv(M)
                    # 初始化存储棋格中心点在原始图像中坐标的列表
                    original_centers = []

                    # 遍历目标矩形中的棋格中心点坐标
                    for (cx, cy) in grid_centers:
                        # 将棋格中心点坐标转换为齐次坐标
                        original_pt = np.array([cx, cy, 1], dtype="float32").reshape(-1, 1)
                        # 使用逆矩阵将目标矩形中的点变换回原始图像
                        original_pt = np.dot(inv_M, original_pt)
                        # 进行归一化处理
                        original_pt = original_pt / original_pt[2]
                        # 将变换后的坐标转换为整数，并添加到列表中
                        original_centers.append((int(original_pt[0].item()), int(original_pt[1].item())))

                    # 使用LAB阈值识别棋子
                    color_categories = []
                    # 遍历棋格中心点坐标
                    for (cx, cy) in original_centers:
                        # 确保采样区域在图像范围内
                        y_min = max(0, cy - SAMPLE_RADIUS)
                        y_max = min(lab_img.shape[0], cy + SAMPLE_RADIUS)
                        x_min = max(0, cx - SAMPLE_RADIUS)
                        x_max = min(lab_img.shape[1], cx + SAMPLE_RADIUS)
                        
                        # 提取LAB采样区域
                        lab_sample_region = lab_img[y_min:y_max, x_min:x_max]
                        
                        # 白棋LAB阈值检测
                        white_mask = cv2.inRange(lab_sample_region, WHITE_LAB_MIN, WHITE_LAB_MAX)
                        white_pixels = cv2.countNonZero(white_mask)
                        
                        # 黑棋LAB阈值检测
                        black_mask = cv2.inRange(lab_sample_region, BLACK_LAB_MIN, BLACK_LAB_MAX)
                        black_pixels = cv2.countNonZero(black_mask)
                        
                        # 根据像素数量判断棋子类型
                        if white_pixels > MIN_PIECE_PIXELS and white_pixels > black_pixels:
                            color_categories.append(2)  # 白棋
                        elif black_pixels > MIN_PIECE_PIXELS and black_pixels > white_pixels:
                            color_categories.append(1)  # 黑棋
                        else:
                            color_categories.append(0)  # 无棋子

                    # 初始化 3x3 的棋盘列表，用于存储棋盘信息
                    board = [[0 for _ in range(3)] for _ in range(3)]
                    # 初始化索引
                    index = 0
                    # 遍历 3x3 的棋格
                    for i in range(3):
                        for j in range(3):
                            # 如果索引在颜色类别列表的有效范围内
                            if index < len(color_categories):
                                # 将颜色类别赋值给棋盘列表
                                board[i][j] = color_categories[index]
                            # 索引加 1
                            index += 1

                    # 打印棋盘信息
                    for row in board:
                        print('|'.join(map(str, row)))
                    print('-' * 5)

                    # 在图像上绘制棋盘轮廓
                    cv2.drawContours(img, [approx], -1, (0, 255, 0), 2)

                    # 绘制棋盘中心区域的识别结果
                    index = 0
                    for center in original_centers:
                        x, y = center
                        if index < len(color_categories):
                            # 根据颜色类别设置文本内容
                            if color_categories[index] == 2:
                                text = '2'
                            elif color_categories[index] == 1:
                                text = '1'
                            else:
                                text = '0'
                            # 在图像上绘制绿色的棋子种类数字
                            cv2.putText(img, text, (x - 5, y + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                        index += 1

    # 定义左右两边的ROI区域（固定位置）
    img_height, img_width = img.shape[:2]
    
    # 左侧ROI区域（4个）
    left_rois = []
    for i in range(4):
        x = 50  # 左边距
        y = 60 + i * 30  # 垂直分布
        left_rois.append((x, y, ROI_WIDTH, ROI_HEIGHT))
    
    # 右侧ROI区域（4个）
    right_rois = []
    for i in range(4):
        x = img_width - ROI_WIDTH - 50  # 右边距
        y = 60 + i * 30  # 垂直分布
        right_rois.append((x, y, ROI_WIDTH, ROI_HEIGHT))
    
    # 识别左侧ROI区域的棋子
    left_pieces = []
    for (x, y, w, h) in left_rois:
        # 确保ROI区域在图像范围内
        if y + h <= img_height and x + w <= img_width:
            # 提取ROI区域
            roi_lab = lab_img[y:y+h, x:x+w]
            
            # 白棋色块检测
            white_mask = cv2.inRange(roi_lab, WHITE_LAB_MIN, WHITE_LAB_MAX)
            white_contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            white_area = sum(cv2.contourArea(c) for c in white_contours if cv2.contourArea(c) > MIN_BLOB_AREA)
            
            # 黑棋色块检测
            black_mask = cv2.inRange(roi_lab, BLACK_LAB_MIN, BLACK_LAB_MAX)
            black_contours, _ = cv2.findContours(black_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            black_area = sum(cv2.contourArea(c) for c in black_contours if cv2.contourArea(c) > MIN_BLOB_AREA)
            
            # 判断棋子类型
            if white_area > black_area and white_area > MIN_BLOB_AREA:
                left_pieces.append(2)  # 白棋
            elif black_area > white_area and black_area > MIN_BLOB_AREA:
                left_pieces.append(1)  # 黑棋
            else:
                left_pieces.append(0)  # 无棋子
        else:
            left_pieces.append(0)  # ROI超出范围
    
    # 识别右侧ROI区域的棋子
    right_pieces = []
    for (x, y, w, h) in right_rois:
        # 确保ROI区域在图像范围内
        if y + h <= img_height and x + w <= img_width:
            # 提取ROI区域
            roi_lab = lab_img[y:y+h, x:x+w]
            
            # 白棋色块检测
            white_mask = cv2.inRange(roi_lab, WHITE_LAB_MIN, WHITE_LAB_MAX)
            white_contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            white_area = sum(cv2.contourArea(c) for c in white_contours if cv2.contourArea(c) > MIN_BLOB_AREA)
            
            # 黑棋色块检测
            black_mask = cv2.inRange(roi_lab, BLACK_LAB_MIN, BLACK_LAB_MAX)
            black_contours, _ = cv2.findContours(black_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            black_area = sum(cv2.contourArea(c) for c in black_contours if cv2.contourArea(c) > MIN_BLOB_AREA)
            
            # 判断棋子类型
            if white_area > black_area and white_area > MIN_BLOB_AREA:
                right_pieces.append(2)  # 白棋
            elif black_area > white_area and black_area > MIN_BLOB_AREA:
                right_pieces.append(1)  # 黑棋
            else:
                right_pieces.append(0)  # 无棋子
        else:
            right_pieces.append(0)  # ROI超出范围

    # 绘制左侧ROI区域和识别结果
    for i, (x, y, w, h) in enumerate(left_rois):
        # 绘制ROI区域边框（蓝色）
        cv2.rectangle(img, (x, y), (x+w, y+h), (255, 0, 0), 2)
        # 显示识别结果
        if i < len(left_pieces):
            if left_pieces[i] == 2:
                text = '2'
            elif left_pieces[i] == 1:
                text = '1'
            else:
                text = '0'
            cv2.putText(img, text, (x + w//2 - 5, y + h//2 + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
    
    # 绘制右侧ROI区域和识别结果
    for i, (x, y, w, h) in enumerate(right_rois):
        # 绘制ROI区域边框（红色）
        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 2)
        # 显示识别结果
        if i < len(right_pieces):
            if right_pieces[i] == 2:
                text = '2'
            elif right_pieces[i] == 1:
                text = '1'
            else:
                text = '0'
            cv2.putText(img, text, (x + w//2 - 5, y + h//2 + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    # 打印放置区域信息
    print("左侧放置区:", left_pieces)
    print("右侧放置区:", right_pieces)
    
    # 发送串口数据
    if valid_board:
        # 发送二进制数据格式
        send_board_data(board, left_pieces, right_pieces)
        # 发送文本数据格式 (可选，注释掉其中一个)
        # send_text_data(board, left_pieces, right_pieces)
    
    # 读取串口接收数据
    read_serial_data()

    # 将处理后的图像转换为 maix.image.Image 对象
    img_show = image.cv2image(img, bgr=True, copy=False)
    # 在显示屏上显示图像
    disp.show(img_show)
