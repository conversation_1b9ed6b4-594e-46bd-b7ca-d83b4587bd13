<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title id="pageTitle">Maix 文件浏览器</title> <!-- 动态标题 -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <button onclick="selectDirectory('/')">/</button>
        <button onclick="selectDirectory('/root')">/root</button>
        <button onclick="selectDirectory('/maixapp')">/maixapp</button>
        <button onclick="selectDirectory('/maixapp/share')">/maixapp/share</button>
    </header>
    <section id="settings">
        <label>
            <input type="checkbox" id="showHidden" onclick="toggleHidden()"> <span id="labelShowHidden"></span>
        </label>
    </section>
    <section id="currentPath">
        <button id="backButton" onclick="goBack()" style="display: none;"></button>
        <span id="pathDisplay"></span>
    </section>
    <section id="fileList"></section>
    <div id="previewOverlay" onclick="closePreview()">
        <button id="closeButton" onclick="closePreview()"></button>
        <div id="previewContent" onclick="event.stopPropagation()">
            <img id="previewImage" src="" alt="Image Preview">
            <pre id="previewText"></pre>
            <video id="previewVideo" controls muted type="video/mp4"></video>
        </div>
        <div id="zoomControls" onclick="event.stopPropagation()">
            <button id="zoomInButton" onclick="zoomIn()"></button>
            <button id="zoomOutButton" onclick="zoomOut()"></button>
            <button id="resetZoomButton" onclick="resetZoom()"></button>
        </div>
    </div>
    <div id="messageBox"></div>

    <script src="script.js"></script>
</body>
</html>
