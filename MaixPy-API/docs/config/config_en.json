{"locale": "en", "navbar": {"title": "MaixPy", "logo": {"alt": "MaixPy logo", "url": ""}, "home_url": "/en/", "items": [{"url": "https://wiki.sipeed.com", "label": "Sipeed Wiki", "position": "left"}, {"url": "/doc/en/index.html", "label": "Documentation", "position": "left"}, {"url": "/api/index.html", "label": "API", "position": "left"}, {"url": "/doc/en/faq.html", "label": "FAQ", "position": "left"}, {"id": "github", "label": "<img src='/maixpy/static/image/github-fill.svg' style='height: 1.5em;vertical-align: middle;'>&nbsp;", "url": "https://github.com/sipeed/maixpy", "position": "right", "target": "_blank"}, {"id": "language", "label": "<img src='/maixpy/static/image/language.svg' style='height: 1.5em;vertical-align: middle;'>&nbsp;", "position": "right", "type": "language"}]}, "footer": {"top": [{"label": "Links", "items": [{"label": "Sipeed Wiki", "url": "https://wiki.sipeed.com", "target": "_blank"}, {"label": "Sipeed Official", "url": "https://www.sipeed.com", "target": "_blank"}, {"label": "MaixHub", "url": "https://maixhub.com/", "target": "_blank"}, {"label": "Site map", "url": "/sitemap.xml"}, {"label": "Generated by teedoc", "url": "https://github.com/neutree/teedoc", "target": "_blank"}]}, {"label": "Source code", "items": [{"label": "MaixPy source code", "url": "https://github.com/sipeed/maixpy", "target": "_blank"}, {"label": "MaixCDK source code", "url": "https://github.com/sipeed/MaixCDK", "target": "_blank"}, {"label": "Wiki source code", "url": "https://github.com/sipeed/sipeed_wiki", "target": "_blank"}, {"label": "Open source projects", "url": "https://github.com/sipeed", "target": "_blank"}]}, {"label": "Follow us", "items": [{"label": "twitter", "url": "https://twitter.com/SipeedIO", "target": "_blank"}, {"label": "Taobao", "url": "https://sipeed.taobao.com/", "target": "_blank"}, {"label": "AliExpress", "url": "https://www.aliexpress.com/store/911876460", "target": "_blank"}, {"label": "github", "url": "https://github.com/sipeed", "target": "_blank"}, {"label": "<a>Wechat </a><img src='/maixpy/static/image/wechat.png'>"}]}, {"label": "Contact us", "items": [{"label": "Tel: +86 0755-27808509"}, {"label": "Bussiness: <EMAIL>"}, {"label": "Addr: 深圳市宝安区新湖路4008号蘅芳科技办公大厦A座-2101C"}, {"label": "Join us", "url": "https://wiki.sipeed.com/join_us.html"}]}], "bottom": [{"label": "©2018-2023 深圳矽速科技有限公司", "url": "https://www.sipeed.com", "target": "_blank"}, {"label": "粤ICP备19015433号", "url": "https://beian.miit.gov.cn/#/Integrated/index", "target": "_blank"}]}}