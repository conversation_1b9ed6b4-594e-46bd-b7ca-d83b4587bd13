---
title: MaixPy bm8653驱动说明
update:
  - date: 2024-08-27
    author: iawak9lkm
    version: 1.0.0
    content: 初版文档
---

## BM8653 简介

BM8653是一款实时时钟（RTC）芯片，广泛应用于各种电子设备中，用于提供精确的时间和日期信息。它具有低功耗、高精度的特点，能够在设备断电的情况下通过备用电池继续运行，确保时间的连续性和准确性。

## MaixPy 中使用 BM8653

在 MaixPy 中使用 BM8653 很简单, 您只需要知道您平台上的 BM8653 挂载在哪个 I2C 总线上. MaixCAM Pro 板载的 BM8563 挂载在 I2C-4 上.

示例代码:

```python
from maix import ext_dev, pinmap, err, time

### Enable I2C
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SCL")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SDA")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)

BM8653_I2CBUS_NUM = 4

rtc = ext_dev.bm8563.BM8563(BM8653_I2CBUS_NUM)

### 2020-12-31 23:59:45
t = [2020, 12, 31, 23, 59, 45]

# Set time
rtc.datetime(t)

while True:
    rtc_now = rtc.datetime()
    print(f"{rtc_now[0]}-{rtc_now[1]}-{rtc_now[2]} {rtc_now[3]}:{rtc_now[4]}:{rtc_now[5]}")
    time.sleep(1)
```

如果您使用的是 MaixCAM Pro 板载的 BM8653, 无需使能 I2C-4.

示例中读写 BM8653, 设置或是读取当前时间.

您也可以通过以下示例将当前 BM8653 内的时间设置为系统时间, 或是将当前系统时间设置为 BM8653 内的时间.

```python
from maix import ext_dev, pinmap, err, time

### Enable I2C
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SCL")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)
# ret = pinmap.set_pin_function("PIN_NAME", "I2Cx_SDA")
# if ret != err.Err.ERR_NONE:
#     print("Failed in function pinmap...")
#     exit(-1)


BM8653_I2CBUS_NUM = 4

rtc = ext_dev.bm8563.BM8563(BM8653_I2CBUS_NUM)

### Update RTC time from system
rtc.systohc()

### Update system time from RTC
# rtc.hctosys()

while True:
    rtc_now = rtc.datetime()
    print(f"{rtc_now[0]}-{rtc_now[1]}-{rtc_now[2]} {rtc_now[3]}:{rtc_now[4]}:{rtc_now[5]}")
    time.sleep(1)
```

**BM8653 的底层实现类似于单例模式, 本 API 可以保证对单个 BM8653 的读写是线程安全的. 也就意味着您可以随意的创建 BM8653 对象, 在任意地方读写 BM8653 均不会产生数据竞争.**

传给 BM8653 对象的 timetuple 遵循 (year, month, day[, hour[, minute[, second]]]), 即必须要有前三个参数, 后续参数缺失部分代表的时间不会进行修改. BM8653 保证返回的 timetuple 为空时表示错误, 不为空时必定是含有6个元素的 List[], 其内容为(year, month, day, hour, minute, second).


有关 BM8653 API 的详细说明请看 [BM8653 API 文档](../../../api/maix/ext_dev/bm8563.md)
