from maix import uart, app, time

def main():
    """串口监听器 - 监听并分析串口数据"""
    print("=== 串口监听器 ===")
    print("用途：监听串口数据并进行详细分析")
    print("支持：文本数据、二进制数据、三角形数据包")
    print("=" * 35)
    
    # 初始化串口
    serial = uart.UART("/dev/ttyS0", 115200)
    print("串口初始化成功")
    print("开始监听...")
    print("-" * 50)
    
    # 统计信息
    total_bytes = 0
    packet_count = 0
    text_count = 0
    binary_count = 0
    triangle_packet_count = 0
    
    buffer = b''  # 数据缓冲区
    
    while not app.need_exit():
        # 读取数据
        data = serial.read(len=-1, timeout=100)
        
        if data:
            total_bytes += len(data)
            buffer += data
            
            print(f"\n📨 收到 {len(data)} 字节数据")
            print(f"原始数据: {data.hex().upper()}")
            
            # 尝试解析为文本
            try:
                text = data.decode('utf-8', errors='ignore').strip()
                if text and all(32 <= ord(c) <= 126 or c in '\r\n\t' for c in text):
                    text_count += 1
                    print(f"📝 文本内容: '{text}'")
                    
                    # 检查特殊指令
                    if text == "?":
                        print("❓ 检测到状态查询!")
                else:
                    binary_count += 1
                    print("🔢 二进制数据")
            except:
                binary_count += 1
                print("🔢 二进制数据")
            
            # 检查特殊二进制指令
            check_binary_commands(data)

            # 检查三角形数据包
            triangle_packets = find_triangle_packets(buffer)
            for packet in triangle_packets:
                triangle_packet_count += 1
                analyze_triangle_packet(packet, triangle_packet_count)
            
            # 清理已处理的数据包
            buffer = remove_processed_packets(buffer)
            
            # 显示统计信息
            if (packet_count + 1) % 10 == 0:
                print_statistics(total_bytes, text_count, binary_count, triangle_packet_count)
            
            packet_count += 1
        
        time.sleep_ms(10)

def check_binary_commands(data):
    """检查二进制指令"""
    if b'\xFF\x00' in data:
        print("🚀 检测到启动指令 FF 00!")
    if b'\xFF\xFF' in data:
        print("🛑 检测到停止指令 FF FF!")

def find_triangle_packets(buffer):
    """在缓冲区中查找三角形数据包"""
    packets = []
    i = 0
    
    while i < len(buffer) - 9:  # 至少需要10字节
        if buffer[i] == 0x78:  # 包头
            # 检查是否有完整的数据包
            if i + 9 < len(buffer) and buffer[i + 9] == 0xFC:  # 包尾
                packet = buffer[i:i+10]
                packets.append(packet)
                i += 10
            else:
                i += 1
        else:
            i += 1
    
    return packets

def analyze_triangle_packet(packet, count):
    """分析三角形数据包"""
    print(f"\n🎯 三角形数据包 #{count}")
    print(f"完整数据包: {packet.hex().upper()}")
    
    # 解析坐标
    target_x = (packet[1] << 8) | packet[2]
    target_y = (packet[3] << 8) | packet[4]
    laser_x = (packet[5] << 8) | packet[6]
    laser_y = (packet[7] << 8) | packet[8]
    
    print(f"🎯 目标点: ({target_x:3d}, {target_y:3d})")
    print(f"🔴 激光点: ({laser_x:3d}, {laser_y:3d})")
    
    # 计算距离和方向
    dx = target_x - laser_x
    dy = target_y - laser_y
    distance = (dx*dx + dy*dy)**0.5
    
    print(f"📏 距离: {distance:.1f} 像素")
    print(f"📐 偏移: X={dx:+d}, Y={dy:+d}")
    
    # 判断激光状态
    if distance < 15:
        print("✅ 激光接近目标点")
    elif distance < 50:
        print("🟡 激光正在接近目标")
    else:
        print("🔴 激光距离目标较远")

def remove_processed_packets(buffer):
    """移除已处理的数据包，保留未完整的数据"""
    # 简单实现：如果缓冲区太大，清空它
    if len(buffer) > 1000:
        return b''
    return buffer

def print_statistics(total_bytes, text_count, binary_count, triangle_count):
    """打印统计信息"""
    print(f"\n📊 统计信息:")
    print(f"   总字节数: {total_bytes}")
    print(f"   文本消息: {text_count}")
    print(f"   二进制数据: {binary_count}")
    print(f"   三角形数据包: {triangle_count}")

if __name__ == "__main__":
    main()
